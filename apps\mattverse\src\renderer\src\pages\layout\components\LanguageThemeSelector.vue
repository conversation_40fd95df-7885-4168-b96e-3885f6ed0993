<template>
  <div class="flex items-center gap-1">
    <!-- 语言选择器 -->
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <Button
          variant="ghost"
          size="sm"
          class="h-8 px-2 hover:bg-accent/50 dark:hover:bg-accent/20 transition-colors duration-200"
        >
          <span class="text-sm">{{ currentLocaleInfo.flag }}</span>
          <span class="ml-1 text-xs font-medium text-foreground/80 dark:text-foreground/90">
            {{ currentLocaleInfo.name }}
          </span>
          <MattIcon
            name="ChevronDown"
            class="ml-1 h-3 w-3 text-muted-foreground/60 dark:text-muted-foreground/80"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        class="w-40 bg-popover/95 dark:bg-popover/95 backdrop-blur-sm border border-border/50 dark:border-border/30"
      >
        <DropdownMenuLabel class="text-xs text-muted-foreground dark:text-muted-foreground/90">
          {{ $t('components.language_selector.title') }}
        </DropdownMenuLabel>
        <DropdownMenuSeparator class="bg-border/50 dark:bg-border/30" />
        <DropdownMenuItem
          v-for="localeOption in SUPPORTED_LOCALES"
          :key="localeOption.code"
          @click="switchLanguage(localeOption.code)"
          :class="{
            'bg-accent/80 dark:bg-accent/40 text-accent-foreground dark:text-accent-foreground':
              localeOption.code === currentLocale,
          }"
          class="text-sm cursor-pointer hover:bg-accent/50 dark:hover:bg-accent/20 transition-colors duration-150"
        >
          <span class="mr-2 text-base">{{ localeOption.flag }}</span>
          <span class="text-foreground dark:text-foreground/95">{{ localeOption.name }}</span>
          <MattIcon
            v-if="localeOption.code === currentLocale"
            name="Check"
            class="ml-auto h-3 w-3 text-primary dark:text-primary/90"
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>

    <!-- 主题切换器 -->
    <DropdownMenu>
      <DropdownMenuTrigger as-child>
        <Button
          variant="ghost"
          size="sm"
          class="h-8 px-2 hover:bg-accent/50 dark:hover:bg-accent/20 transition-colors duration-200"
        >
          <MattIcon
            :name="currentThemeIcon"
            :class="[
              'h-4 w-4 transition-colors duration-200',
              currentTheme === 'light'
                ? 'text-amber-500 dark:text-amber-400'
                : currentTheme === 'dark'
                  ? 'text-blue-400 dark:text-blue-300'
                  : 'text-muted-foreground dark:text-muted-foreground/80',
            ]"
          />
          <MattIcon
            name="ChevronDown"
            class="ml-1 h-3 w-3 text-muted-foreground/60 dark:text-muted-foreground/80"
          />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        class="w-36 bg-popover/95 dark:bg-popover/95 backdrop-blur-sm border border-border/50 dark:border-border/30"
      >
        <DropdownMenuLabel class="text-xs text-muted-foreground dark:text-muted-foreground/90">
          {{ $t('components.theme_selector.title') }}
        </DropdownMenuLabel>
        <DropdownMenuSeparator class="bg-border/50 dark:bg-border/30" />
        <DropdownMenuItem
          v-for="themeOption in themeOptions"
          :key="themeOption.value"
          @click="switchTheme(themeOption.value)"
          :class="{
            'bg-accent/80 dark:bg-accent/40 text-accent-foreground dark:text-accent-foreground':
              themeOption.value === selectedTheme,
          }"
          class="text-sm cursor-pointer hover:bg-accent/50 dark:hover:bg-accent/20 transition-colors duration-150"
        >
          <MattIcon
            :name="themeOption.icon"
            :class="[
              'mr-2 h-4 w-4 transition-colors duration-200',
              themeOption.value === 'light'
                ? 'text-amber-500 dark:text-amber-400'
                : themeOption.value === 'dark'
                  ? 'text-blue-400 dark:text-blue-300'
                  : 'text-muted-foreground dark:text-muted-foreground/80',
            ]"
          />
          <span class="text-foreground dark:text-foreground/95">{{ themeOption.label }}</span>
          <MattIcon
            v-if="themeOption.value === selectedTheme"
            name="Check"
            class="ml-auto h-3 w-3 text-primary dark:text-primary/90"
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n, SUPPORTED_LOCALES, type SupportedLocale } from '@mattverse/i18n'
import { useSettingsStore, type ThemeMode } from '@/store'
import { setDocumentLang } from '@mattverse/i18n'
import { logger } from '@mattverse/shared'

const { t, locale } = useI18n()
const settingsStore = useSettingsStore()

// 语言相关
const currentLocale = computed(() => locale.value as SupportedLocale)

const currentLocaleInfo = computed(() => {
  return SUPPORTED_LOCALES.find(l => l.code === currentLocale.value) || SUPPORTED_LOCALES[0]
})

const switchLanguage = (newLocale: SupportedLocale) => {
  // 更新设置状态
  settingsStore.setLanguage(newLocale)

  // 更新i18n实例的语言
  locale.value = newLocale

  // 设置文档语言
  setDocumentLang(newLocale)

  logger.info(`语言已切换为 ${newLocale}`)
}

// 主题相关
const selectedTheme = computed(() => settingsStore.theme)
const currentTheme = computed(() => settingsStore.currentTheme)

const currentThemeIcon = computed(() => {
  const option = themeOptions.value.find(opt => opt.value === currentTheme.value)
  return option?.icon || 'Monitor'
})

const themeOptions = computed(() => [
  {
    value: 'light' as ThemeMode,
    label: t('settings.theme_options.light'),
    icon: 'Sun',
  },
  {
    value: 'dark' as ThemeMode,
    label: t('settings.theme_options.dark'),
    icon: 'Moon',
  },
  {
    value: 'system' as ThemeMode,
    label: t('settings.theme_options.system'),
    icon: 'Monitor',
  },
])

const switchTheme = (themeValue: ThemeMode) => {
  settingsStore.setTheme(themeValue)
}
</script>
