syntax = "proto3";
package matt;

import "common.proto";

message GeneralRequest{
  string task_id = 1;
  string server_id = 2;
  string service_name = 3;
  string user_id = 4;
  string token = 5;
  bool is_save = 6;
  map<string, common.ValueType> key_type_pairs = 7;
  map<string, string> key_value_pairs = 8;
}

message GeneralResponse {
  int32 status_code = 1;
  string message = 2;
  map<string, common.ValueType> key_type_pairs = 3;
  map<string, string> key_value_pairs = 4;
}

message SubmitResponse {
  int32 status_code = 1;
  string message = 2;
  string task_id = 3;
}

message TaskOperateRequest {
  string task_id = 1;
  int32 task_pid = 2;
  TaskOperateCode operate_code = 3;
}

enum TaskOperateCode {
  Stop = 0;
  Pause = 1;
  Resume = 2;
  Restart = 3;
}

enum ServerOperateCode {
  ServerStop = 0;
  ServerStart = 1;
  ServerRestart = 2;
  ServerUsage = 3;
}

message ServerOperateRequest {
  ServerOperateCode operate_code = 1;
}

message AgentRequest {
  string user_id = 1;
  string token = 2;
  string server_id = 3;
  string service_name = 4;
  string session_id = 5;
  string model_type = 6;
  bool is_stream_response = 7;
  map<string,string> messages = 8;
}

message AgentResponse {
  common.ResponseStatus status = 1;
  string message = 2;
  string session_id = 3;
  string result = 4;
}

message ClientApiRequest {
  string user_id = 1;
  string session_id = 2;
  string command = 3;
  string target_id = 4;
  string task_id = 5;
  string params = 6;
  string timestamp = 7;
  string result = 8;
  string status = 9;
  string message = 10;
}

service MattService {
  rpc defaultService (GeneralRequest) returns (GeneralResponse);
  //rpc clientStreamService (stream GeneralRequest) returns (GeneralResponse);
  rpc serverStreamService (GeneralRequest) returns (stream GeneralResponse);
  //rpc bidirectionalStreamService (stream GeneralRequest) returns (stream GeneralResponse);
  rpc submitService (GeneralRequest) returns (SubmitResponse);
  rpc ping(common.PingRequest) returns (common.PingResponse);
  rpc taskService(TaskOperateRequest) returns (common.OperateResponse);
  rpc serverService(ServerOperateRequest) returns (common.ServerOperateResponse);
  rpc agentService (AgentRequest) returns (stream AgentResponse);
}
