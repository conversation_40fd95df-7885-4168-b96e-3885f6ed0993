<template>
  <div id="app" class="w-full h-full">
    <!-- 导航栏 -->
    <nav class="bg-gray-800 text-white p-4">
      <div class="flex items-center justify-between">
        <h1 class="text-xl font-bold">HighPower</h1>
        <div class="flex space-x-4">
          <router-link
            to="/"
            class="px-3 py-2 rounded hover:bg-gray-700"
            :class="{ 'bg-gray-700': $route.path === '/' }"
          >
            首页
          </router-link>
          <router-link
            to="/workflow"
            class="px-3 py-2 rounded hover:bg-gray-700"
            :class="{ 'bg-gray-700': $route.path === '/workflow' }"
          >
            工作流编辑器
          </router-link>
          <router-link
            to="/workflow-advanced"
            class="px-3 py-2 rounded hover:bg-gray-700"
            :class="{ 'bg-gray-700': $route.path === '/workflow-advanced' }"
          >
            高级编辑器
          </router-link>
          <router-link
            to="/grpc-test"
            class="px-3 py-2 rounded hover:bg-gray-700"
            :class="{ 'bg-gray-700': $route.path === '/grpc-test' }"
          >
            gRPC 测试
          </router-link>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <main class="flex-1 overflow-hidden">
      <router-view />
    </main>
  </div>
</template>

<script setup lang="ts">
// 这里可以添加全局的逻辑
</script>

<style>
#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
  overflow: hidden;
}
</style>
