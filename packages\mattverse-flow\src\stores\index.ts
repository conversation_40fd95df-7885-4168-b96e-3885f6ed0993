import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

export * from './flow'
export * from './workflow'
export * from './flowSettings'
export * from './nodeNavbar'
export * from './nodeModules'
export * from './tools'
export * from './task'

// 导出持久化配置
export * from './plugins/persist-config'

//仅用于 flow 包的独立使用
export const createFlowPinia = () => {
  const pinia = createPinia()
  pinia.use(piniaPluginPersistedstate)
  return pinia
}

export const flowPersistPlugin = piniaPluginPersistedstate

/**
 * 辅助函数：为现有的 Pinia 实例添加持久化支持
 * @param pinia 现有的 Pinia 实例
 */
export const setupFlowPersistence = (pinia: any) => {
  // 检查是否已经有持久化插件
  if (!pinia._p._plugins.find((p: any) => p.key === 'persistedstate')) {
    pinia.use(flowPersistPlugin)
  }
  return pinia
}
