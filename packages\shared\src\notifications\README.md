# 统一通知系统

基于 vue-sonner API 设计的统一通知系统，支持应用内通知、桌面通知和声音通知。

## 特性

- 🎯 **统一 API**：基于 vue-sonner 的标准 API 设计
- 📱 **应用内通知**：使用 vue-sonner 的 Toast 组件
- 🖥️ **桌面通知**：Electron 原生桌面通知
- 🔊 **声音通知**：基于 Web Audio API 的音效系统
- ⚙️ **灵活配置**：支持独立控制各种通知类型
- 🎨 **主题支持**：支持亮色/暗色主题
- 📦 **模块化**：可在多个应用间共享

## 安装和初始化

### 1. 在应用启动时初始化

```typescript
// main.ts
import { toast } from 'vue-sonner'
import { initNotificationService, logger } from '@mattverse/shared'

// 初始化通知服务
initNotificationService(toast, logger, {
  enableNotifications: true,    // 应用内通知
  desktopNotifications: true,   // 系统通知
  soundNotifications: false     // 声音通知
})
```

### 2. 在组件中使用

```vue
<template>
  <div>
    <Toaster position="top-right" />
    <button @click="showNotification">显示通知</button>
  </div>
</template>

<script setup lang="ts">
import { Toaster } from 'vue-sonner'
import { notify } from '@mattverse/shared'

const showNotification = async () => {
  await notify.success('操作成功', {
    description: '数据已保存到服务器',
    duration: 5000
  })
}
</script>
```

## API 参考

### 基础方法

```typescript
// 显示不同类型的通知
await notify.success('成功消息', options)
await notify.error('错误消息', options)
await notify.warning('警告消息', options)
await notify.info('信息消息', options)
await notify.loading('加载中...', options)

// 自定义通知
await notify.show('自定义消息', {
  type: 'default',
  ...options
})

// 关闭通知
notify.dismiss(id)        // 关闭指定通知
notify.dismiss()          // 关闭所有通知
```

### 通知选项

```typescript
interface BaseNotificationOptions {
  // 基础属性
  id?: string | number
  title?: string
  description?: string
  duration?: number
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
  
  // 样式相关
  style?: Record<string, string>
  class?: string
  descriptionClass?: string
  unstyled?: boolean
  
  // 功能相关
  action?: { label: string; onClick: () => void }
  cancel?: { label: string; onClick: () => void }
  dismissible?: boolean
  closeButton?: boolean
  
  // 回调函数
  onDismiss?: (toast: { id: string | number }) => void
  onAutoClose?: (toast: { id: string | number }) => void
  
  // 自定义属性
  icon?: string | any
  richColors?: boolean
  
  // 扩展属性 - 控制通知类型
  showToast?: boolean      // 是否显示应用内通知
  showDesktop?: boolean    // 是否显示桌面通知
  playSound?: boolean      // 是否播放声音
}
```

## 使用示例

### 基础用法

```typescript
// 简单成功通知
await notify.success('保存成功')

// 带描述的通知
await notify.error('保存失败', {
  description: '网络连接超时，请重试'
})

// 自定义持续时间
await notify.info('重要提醒', {
  description: '系统将在5分钟后重启',
  duration: 10000
})
```

### 高级用法

```typescript
// 只显示桌面通知，不显示应用内通知
await notify.warning('系统警告', {
  description: '磁盘空间不足',
  showToast: false,
  showDesktop: true
})

// 带动作按钮的通知
await notify.info('新版本可用', {
  description: '点击更新到最新版本',
  action: {
    label: '立即更新',
    onClick: () => {
      // 执行更新逻辑
    }
  }
})

// 永久显示的通知
await notify.error('严重错误', {
  description: '请联系管理员',
  duration: Infinity,
  closeButton: true
})
```

### 设置管理

```typescript
import { getNotificationService } from '@mattverse/shared'

const service = getNotificationService()

// 更新设置
service.updateSettings({
  enableNotifications: false,  // 禁用应用内通知
  desktopNotifications: true,  // 启用桌面通知
  soundNotifications: true     // 启用声音通知
})

// 获取当前设置
const settings = service.getSettings()
console.log(settings)
```

### 声音通知

```typescript
import { notificationSounds } from '@mattverse/shared'

// 播放不同类型的音效
notificationSounds.play('success')
notificationSounds.play('error')
notificationSounds.play('warning')
notificationSounds.play('info')

// 控制声音开关
notificationSounds.setEnabled(false)
```

## 架构设计

```
packages/shared/src/notifications/
├── index.ts                           # 入口文件
├── notification-service.ts            # 基础服务和类型定义
├── electron-notification-service.ts   # Electron 环境实现
├── notification-sounds.ts             # 声音通知系统
└── README.md                          # 文档
```

## 类型定义

所有类型定义都基于 vue-sonner 的 API 标准，确保与现有 toast 系统的完全兼容性。

## 注意事项

1. **初始化顺序**：必须在使用通知功能前调用 `initNotificationService`
2. **权限处理**：桌面通知会自动处理权限请求
3. **错误处理**：所有通知方法都包含完整的错误处理
4. **性能优化**：声音系统使用 Web Audio API，性能优异
5. **兼容性**：完全兼容现有的 vue-sonner 使用方式
