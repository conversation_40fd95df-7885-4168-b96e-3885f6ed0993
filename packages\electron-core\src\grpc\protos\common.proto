syntax = "proto3";
package common;

enum ResponseStatus {
  Success = 0;
  Failed = 1;
}

enum UpdateType {
  Append = 0;
  Overwrite = 1;
}

enum ValueType {
  Int32 = 0;
  Int64 = 1;
  Uint32 = 2;
  Uint64 = 3;
  Float = 4;
  Double = 5;
  String = 7;
  Json = 8;
  Jsonarray = 9;
  Bool = 10;
}

message PingRequest {}
message PingResponse {
  bool success = 1;
}

message GetClientUrlResponse {
  string client_url =1 ;
}

message GetRequest {
  string user_id = 1;
  string token = 2;
  string id = 3;
}

message GetResponse {
  ResponseStatus status = 1;
  string message = 2;
  string result = 3;
  ValueType result_type = 4;
}

message UpdateRequest {
  string user_id = 1;
  string token = 2;
  string id = 3;
  UpdateType type = 4;
}

message OperateRequest {
  string user_id = 1;
  string token = 2;
  string id = 3;
}

message OperateResponse {
  ResponseStatus status = 1;
  string message = 2;
}

message GpuStatus {
  int32 index = 1;
  string model = 2;
  float usage = 3;
  float used_memory = 4;
  float free_memory = 5;
  float total_memory = 6;
  float memory_usage = 7;
}

message MemoryStatus {
  float total_memory = 1;
  float free_memory = 2;
  float used_memory = 3;
  string unit = 4;
}

enum ServerOperateCode {
  ServerStop = 0;
  ServerStart = 1;
  ServerRestart = 2;
  ServerGetUsage = 3;
}

message ServerOperateRequest {
  ServerOperateCode operate_code = 1;
}

message ServerUsage {
  float cpu_usage = 2;
  MemoryStatus memory_status = 3;
  repeated GpuStatus gpu_status_list = 4;
}

message ServerOperateResponse {
  ResponseStatus status = 1;
  string message = 2;
  ServerUsage server_usage = 3;
}


service CommonService {
  rpc ping (PingRequest) returns (PingResponse);
}
