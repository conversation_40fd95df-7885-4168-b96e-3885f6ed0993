/**
 * 节点相关类型定义
 */
import type { Position, Dimensions } from './base'

/**
 * 节点类型
 */
export type NodeType = 'start' | 'end' | 'process' | 'decision' | 'custom'

/**
 * 节点数据
 */
export interface NodeData {
  name: string
  icon: string | { type: string; value: string }
  type: string
  description?: string
  categories: Array<{
    name: string
    nodes: Array<any>
  }>
  enabled?: boolean
  isBuiltin?: boolean
  [key: string]: any
}

/**
 * 工作流节点
 */
export interface WorkflowNode {
  id: string
  type: NodeType
  position: Position
  data: NodeData
  width?: number
  height?: number
  selected?: boolean
  dragging?: boolean
  resizing?: boolean
  connectable?: boolean
  deletable?: boolean
  selectable?: boolean
  focusable?: boolean
  zIndex?: number
  extent?: 'parent' | [number, number, number, number]
  expandParent?: boolean
  positionAbsolute?: Position
  ariaLabel?: string
  style?: Record<string, any>
  class?: string
  hidden?: boolean
  createdAt?: Date
  updatedAt?: Date
}
