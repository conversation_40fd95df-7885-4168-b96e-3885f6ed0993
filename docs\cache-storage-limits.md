# 缓存存储类型大小限制说明

## Electron 环境下的实际存储限制

基于 Electron 的实际测试和官方文档，以下是各种存储类型的真实限制：

### 1. HTTP 缓存 (1GB 默认)
- **实际限制**: Electron 中无硬性限制，受磁盘空间影响
- **推荐设置**: 1-2GB
- **用途**: 网络请求缓存、静态资源缓存

### 2. IndexedDB (10GB 默认) ⭐
- **实际限制**:
  - **Chrome/Chromium**: 可达可用磁盘空间的 80%（如 100GB 磁盘上可达 60GB）
  - **Firefox**: 桌面版约 2GB，移动版初始 5MB
  - **Safari**: iOS 约 1GB，桌面版更宽松
  - **Electron**: 基于 Chromium，可以非常大
- **推荐设置**: 10-50GB（适合大量本地数据存储）
- **用途**: 结构化数据存储、离线数据、文档缓存、媒体文件

### 3. LocalStorage (50MB 默认)
- **实际限制**:
  - **单个值限制**: 10MB（超出抛出 QuotaExceededError）
  - **总存储**: 可以超过 10MB，但要考虑性能影响
  - **Electron 环境**: 相对宽松，但同步操作可能阻塞 UI
- **推荐设置**: 50MB（总存储）
- **用途**: 简单键值对存储、应用状态持久化
- **注意**: 避免单个值超过 10MB，考虑拆分大数据

### 4. SessionStorage (50MB 默认)
- **实际限制**: 与 LocalStorage 类似
  - **单个值限制**: 10MB
  - **总存储**: 可以更大，但会话结束即清除
- **推荐设置**: 50MB
- **用途**: 会话期间的临时数据、表单状态
- **注意**: 内存限制，会话结束即清除

### 5. Cookies (1MB 默认)
- **实际限制**:
  - **每个域名**: 约 4KB 总大小
  - **每个 Cookie**: 最大 4KB
  - **总数限制**: 每个域名通常 50-300 个
- **推荐设置**: 1MB（考虑多个域名的总和）
- **用途**: 认证信息、用户偏好、会话标识
- **注意**: 实际可用空间非常有限，超出会被浏览器忽略

### 6. Service Workers (500MB 默认)
- **实际限制**: 相对宽松，可以缓存大量资源
- **推荐设置**: 500MB-1GB
- **用途**: 离线资源缓存、PWA 功能、静态资源

### 7. AppCache (100MB 默认)
- **状态**: 已废弃，但可能仍存在
- **推荐设置**: 100-200MB
- **用途**: 遗留的离线缓存

### 8. Pinia Store (50MB 默认)
- **限制**: 内存限制
- **推荐设置**: 50-100MB
- **用途**: 应用状态管理、响应式数据

### 9. 应用设置缓存 (10MB 默认)
- **限制**: 配置文件大小
- **推荐设置**: 10-20MB
- **用途**: 用户配置、应用设置、主题数据

## 重要注意事项

### ⚠️ 常见错误
- **QuotaExceededError**: LocalStorage/SessionStorage 超出限制
- **Cookie 丢失**: 超出每域名 4KB 限制
- **性能问题**: IndexedDB 数据过大导致启动缓慢

### 💡 最佳实践

#### IndexedDB 大数据存储
1. **使用 Storage Estimation API**:
   ```javascript
   const quota = await navigator.storage.estimate();
   console.log('总配额:', quota.quota);
   console.log('已使用:', quota.usage);
   ```
2. **请求持久化存储**:
   ```javascript
   const persistent = await navigator.storage.persist();
   console.log('持久化存储:', persistent);
   ```
3. **数据压缩**: 对大型 JSON 数据使用压缩算法
4. **分批处理**: 大量数据分批写入，避免内存溢出
5. **索引优化**: 合理设计 IndexedDB 索引提高查询性能

#### 通用最佳实践
1. **定期清理**: 启用自动清理避免累积过多数据
2. **监控使用量**: 定期检查各存储类型的使用情况
3. **合理分配**: 根据实际需求设置限制
4. **错误处理**: 处理 QuotaExceededError 等存储异常
5. **渐进式降级**: 存储不足时提供备选方案

### 🔧 调试建议
- 使用 Electron DevTools 监控存储使用情况
- 在开发环境中测试存储限制
- 记录存储相关的错误和警告
