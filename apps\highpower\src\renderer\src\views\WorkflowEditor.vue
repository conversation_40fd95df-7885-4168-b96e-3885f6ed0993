<template>
  <div class="workflow-editor">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <button @click="createNewWorkflowHandler" class="btn btn-primary">
          <PlusIcon class="w-4 h-4" />
          新建工作流
        </button>
        <button @click="saveWorkflow" :disabled="!canSave" class="btn btn-secondary">
          <SaveIcon class="w-4 h-4" />
          保存
        </button>
        <button @click="executeWorkflow" :disabled="!canExecute" class="btn btn-success">
          <PlayIcon class="w-4 h-4" />
          执行
        </button>
      </div>

      <div class="toolbar-right">
        <select v-model="selectedWorkflowId" @change="loadSelectedWorkflow" class="select">
          <option value="">选择工作流</option>
          <option v-for="workflow in workflowList" :key="workflow.id" :value="workflow.id">
            {{ workflow.name }}
          </option>
        </select>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 侧边栏 - 节点面板 -->
      <div class="sidebar">
        <div class="node-palette">
          <h3>节点类型</h3>
          <div class="node-types">
            <div
              v-for="nodeType in nodeTypes"
              :key="nodeType.type"
              class="node-type-item"
              draggable="true"
              @dragstart="onNodeDragStart($event, nodeType)"
            >
              <component :is="nodeType.icon" class="w-5 h-5" />
              <span>{{ nodeType.label }}</span>
            </div>
          </div>
        </div>

        <!-- 属性面板 -->
        <div class="properties-panel" v-if="selectedElements.nodes.length > 0">
          <h3>节点属性</h3>
          <div v-for="node in selectedElements.nodes" :key="node.id" class="property-group">
            <label>标签</label>
            <input
              v-model="node.data.label"
              @input="updateNodeData(node.id, { label: $event.target.value })"
              class="input"
            />
            <label>描述</label>
            <textarea
              v-model="node.data.description"
              @input="updateNodeData(node.id, { description: $event.target.value })"
              class="textarea"
            />
          </div>
        </div>
      </div>

      <!-- 画布区域 -->
      <div class="canvas-container">
        <FlowCanvas
          v-model="flowElements"
          :theme="theme"
          :show-background="true"
          :show-controls="true"
          :show-mini-map="true"
          @node-click="onNodeClick"
          @edge-click="onEdgeClick"
          @pane-click="onPaneClick"
          @connect="onConnect"
          @drop="onCanvasDrop"
          @dragover="onCanvasDragOver"
        />
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span v-if="currentWorkflow"> 当前工作流: {{ currentWorkflow.name }} </span>
        <span v-if="isDirty" class="dirty-indicator">*</span>
      </div>

      <div class="status-right">
        <span>节点: {{ nodes.length }}</span>
        <span>连接: {{ edges.length }}</span>
        <span v-if="isExecuting" class="executing">执行中...</span>
      </div>
    </div>

    <!-- 执行结果弹窗 -->
    <div v-if="showExecutionResult" class="execution-modal" @click="closeExecutionResult">
      <div class="modal-content" @click.stop>
        <h3>执行结果</h3>
        <div v-if="executionResult">
          <p><strong>状态:</strong> {{ executionResult.success ? '成功' : '失败' }}</p>
          <p><strong>耗时:</strong> {{ executionResult.duration }}ms</p>
          <div v-if="executionResult.error" class="error">
            <strong>错误:</strong> {{ executionResult.error }}
          </div>
          <div class="logs">
            <h4>执行日志:</h4>
            <div v-for="log in executionResult.logs" :key="log.id" :class="`log-${log.level}`">
              [{{ log.timestamp.toLocaleTimeString() }}] {{ log.message }}
            </div>
          </div>
        </div>
        <button @click="closeExecutionResult" class="btn btn-primary">关闭</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  useFlowStores,
  useFlowEditor,
  useFlowExecution,
  type WorkflowNode,
  type WorkflowEdge,
  type WorkflowExecutionResult,
} from '@mattverse/mattverse-flow'
// 从包导入 Vue 组件
import { FlowCanvas } from '@mattverse/mattverse-flow'
import {
  PlusIcon,
  SaveIcon,
  PlayIcon,
  CircleIcon,
  SquareIcon,
  DiamondIcon,
  StopCircleIcon,
} from 'lucide-vue-next'

// 状态管理
const { flow, workflow, execution } = useFlowStores()
const { createNewWorkflow, loadWorkflowToEditor, saveCurrentWorkflow } = useFlowEditor()
const { executeCurrentWorkflow } = useFlowExecution()

// 响应式数据
const selectedWorkflowId = ref<string>('')
const theme = ref<'light' | 'dark'>('light')
const showExecutionResult = ref(false)
const executionResult = ref<WorkflowExecutionResult | null>(null)

// 计算属性
const flowElements = computed({
  get: () => [...flow.nodes, ...flow.edges],
  set: elements => {
    // 这里可以处理元素更新
  },
})

const nodes = computed(() => flow.nodes)
const edges = computed(() => flow.edges)
const selectedElements = computed(() => ({
  nodes: flow.selectedNodes.map(id => flow.nodes.find(n => n.id === id)).filter(Boolean),
  edges: flow.selectedEdges.map(id => flow.edges.find(e => e.id === id)).filter(Boolean),
}))
const currentWorkflow = computed(() => workflow.currentWorkflow)
const workflowList = computed(() => workflow.workflowList)
const isDirty = computed(() => flow.isDirty)
const isExecuting = computed(() => execution.isExecuting)

const canSave = computed(() => currentWorkflow.value && isDirty.value)
const canExecute = computed(() => currentWorkflow.value && nodes.value.length > 0)

// 节点类型配置
const nodeTypes = [
  { type: 'start', label: '开始', icon: CircleIcon },
  { type: 'process', label: '处理', icon: SquareIcon },
  { type: 'decision', label: '决策', icon: DiamondIcon },
  { type: 'end', label: '结束', icon: StopCircleIcon },
]

// 事件处理
const createNewWorkflowHandler = () => {
  const name = prompt('请输入工作流名称:')
  if (name) {
    const newWorkflow = createNewWorkflow(name)
    selectedWorkflowId.value = newWorkflow.id
  }
}

const loadSelectedWorkflow = () => {
  if (selectedWorkflowId.value) {
    loadWorkflowToEditor(selectedWorkflowId.value)
  }
}

const saveWorkflow = async () => {
  try {
    await saveCurrentWorkflow()
    alert('保存成功!')
  } catch (error) {
    alert(`保存失败: ${error.message}`)
  }
}

const executeWorkflow = async () => {
  try {
    const result = await executeCurrentWorkflow()
    executionResult.value = result
    showExecutionResult.value = true
  } catch (error) {
    alert(`执行失败: ${error.message}`)
  }
}

const onNodeClick = (node: WorkflowNode, event: MouseEvent) => {
  console.log('Node clicked:', node)
}

const onEdgeClick = (edge: WorkflowEdge, event: MouseEvent) => {
  console.log('Edge clicked:', edge)
}

const onPaneClick = (event: MouseEvent) => {
  flow.clearSelection()
}

const onConnect = (connection: any) => {
  flow.addEdge({
    source: connection.source,
    target: connection.target,
    data: {},
  })
}

const updateNodeData = (nodeId: string, data: any) => {
  flow.updateNode(nodeId, { data: { ...flow.nodes.find(n => n.id === nodeId)?.data, ...data } })
}

// 拖拽处理
const onNodeDragStart = (event: DragEvent, nodeType: any) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/vueflow', JSON.stringify(nodeType))
    event.dataTransfer.effectAllowed = 'move'
  }
}

const onCanvasDragOver = (event: DragEvent) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move'
  }
}

const onCanvasDrop = (event: DragEvent) => {
  event.preventDefault()

  const data = event.dataTransfer?.getData('application/vueflow')
  if (data) {
    const nodeType = JSON.parse(data)
    const rect = (event.target as Element).getBoundingClientRect()

    flow.addNode({
      type: nodeType.type,
      position: {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      },
      data: {
        label: nodeType.label,
        description: '',
      },
    })
  }
}

const closeExecutionResult = () => {
  showExecutionResult.value = false
  executionResult.value = null
}

// 生命周期
onMounted(() => {
  // 初始化一些示例数据
  if (workflow.workflowList.length === 0) {
    const sampleWorkflow = workflow.createWorkflow('示例工作流', {
      description: '这是一个示例工作流',
    })
    selectedWorkflowId.value = sampleWorkflow.id
    loadWorkflowToEditor(sampleWorkflow.id)

    // 添加一些示例节点
    flow.addNode({
      type: 'start',
      position: { x: 100, y: 100 },
      data: { label: '开始' },
    })

    flow.addNode({
      type: 'process',
      position: { x: 300, y: 100 },
      data: { label: '处理数据' },
    })

    flow.addNode({
      type: 'end',
      position: { x: 500, y: 100 },
      data: { label: '结束' },
    })
  }
})
</script>

<style scoped>
.workflow-editor {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e5e5e5;
  gap: 1rem;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
}

.node-palette {
  padding: 1rem;
  border-bottom: 1px solid #e5e5e5;
}

.node-palette h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.node-types {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.node-type-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border: 1px solid #e5e5e5;
  border-radius: 0.25rem;
  cursor: grab;
  transition: background-color 0.2s;
}

.node-type-item:hover {
  background: #f5f5f5;
}

.node-type-item:active {
  cursor: grabbing;
}

.properties-panel {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
}

.properties-panel h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.property-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.property-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.canvas-container {
  flex: 1;
  position: relative;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  background: white;
  border-top: 1px solid #e5e5e5;
  font-size: 0.875rem;
  color: #6b7280;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.dirty-indicator {
  color: #ef4444;
  font-weight: bold;
}

.executing {
  color: #10b981;
  font-weight: 500;
}

.execution-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-content h3 {
  margin: 0 0 1rem 0;
}

.logs {
  margin-top: 1rem;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e5e5;
  border-radius: 0.25rem;
  padding: 0.5rem;
}

.logs h4 {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
}

.log-info {
  color: #3b82f6;
}
.log-warn {
  color: #f59e0b;
}
.log-error {
  color: #ef4444;
}
.log-debug {
  color: #6b7280;
}

.error {
  color: #ef4444;
  margin: 1rem 0;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #059669;
}

.select,
.input,
.textarea {
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.select:focus,
.input:focus,
.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 60px;
}
</style>
