<template>
  <Form class="space-y-6" @submit="onSubmit">
    <div class="space-y-2">
      <h3 class="text-sm font-medium text-muted-foreground">分类信息</h3>
      <Separator />
    </div>
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-12 md:col-span-6">
        <div class="space-y-2">
          <label
            class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >所属模块</label
          >
          <Input :model-value="moduleName" disabled />
        </div>
      </div>

      <div class="col-span-12 md:col-span-6">
        <FormField v-slot="{ componentField }" name="name">
          <FormItem>
            <FormLabel>分类名称</FormLabel>
            <FormControl>
              <Input placeholder="分类名称" v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </div>

      <div class="col-span-12 md:col-span-6">
        <FormField v-slot="{ componentField }" name="sort">
          <FormItem>
            <FormLabel>排序</FormLabel>
            <FormControl>
              <Input type="number" placeholder="0" v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </div>
    </div>

    <div v-if="!props.hideActions" class="flex justify-between">
      <Button type="button" variant="destructive" @click="emit('remove')">删除分类</Button>
      <div class="flex gap-2">
        <Button type="button" variant="outline" @click="onReset">重置</Button>
        <Button type="submit">保存</Button>
      </div>
    </div>
  </Form>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import { watch } from 'vue'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mattverse/mattverse-ui'

const props = withDefaults(
  defineProps<{
    moduleName: string
    hideActions?: boolean
  }>(),
  {
    hideActions: false,
  }
)
const model = defineModel<{ name: string; sort?: number }>()
const emit = defineEmits<{ (e: 'save'): void; (e: 'reset'): void; (e: 'remove'): void }>()

const schema = z.object({
  name: z.string().min(1, '请输入分类名称'),
  sort: z.number({ invalid_type_error: '排序需为数字' }).min(0, '排序需为非负数').optional(),
})

const { handleSubmit, resetForm, values } = useForm({
  validationSchema: toTypedSchema(schema),
  initialValues: { ...(model.value as any) },
})

// 监听 model 变化，同步到表单（单向同步，避免循环）
watch(
  () => model.value,
  newModel => {
    if (newModel) {
      // 重置表单为新的值
      resetForm({ values: { ...(newModel as any) } })
    }
  },
  { deep: true, immediate: true }
)

// 在表单提交时同步到 model
const syncToModel = () => {
  if (model.value) {
    Object.assign(model.value as any, values)
  }
}

function onReset() {
  resetForm({ values: { ...(model.value as any) } })
  emit('reset')
}

const onSubmit = handleSubmit(() => {
  syncToModel()
  emit('save')
})
</script>

<style scoped></style>
