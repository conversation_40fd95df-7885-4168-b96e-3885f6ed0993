# Mattverse Monorepo 配置

# 基本工作空间配置
shared-workspace-lockfile=true
prefer-workspace-packages=true
save-workspace-protocol=true

# 依赖提升配置
hoist=true
hoist-pattern[]=vue*
hoist-pattern[]=@vue*
hoist-pattern[]=pinia*
hoist-pattern[]=@vueuse/*
hoist-pattern[]=axios
hoist-pattern[]=dayjs
hoist-pattern[]=nanoid
hoist-pattern[]=zod
hoist-pattern[]=lucide-vue-next
hoist-pattern[]=radix-vue
hoist-pattern[]=electron*
hoist-pattern[]=@electron-toolkit/*
hoist-pattern[]=@grpc/*
hoist-pattern[]=protobufjs
hoist-pattern[]=prismjs

# 依赖管理
auto-install-peers=true
strict-peer-dependencies=false
resolution-mode=highest

# 网络配置
network-timeout=60000
fetch-retries=3
registry=https://registry.npmmirror.com/

# Electron 镜像配置
electron_mirror=https://npmmirror.com/mirrors/electron/
electron-builder-binaries_mirror=https://npmmirror.com/mirrors/electron-builder-binaries/

# 日志和进度
loglevel=info
progress=true

# 缓存配置
# store-dir=~/.pnpm-store
# cache-dir=~/.pnpm-cache
