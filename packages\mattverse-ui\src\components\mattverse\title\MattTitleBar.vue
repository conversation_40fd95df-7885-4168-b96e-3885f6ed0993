<template>
  <header
    class="relative z-[1000] grid grid-cols-[auto_1fr_auto] items-center px-2 select-none -webkit-user-select-none border-b border-black/[0.06] drag-region [-webkit-app-region:drag]"
    :class="[headerHeightClass, { 'is-mac': isMac, 'is-windows': isWindows, 'is-linux': isLinux }]"
    :style="{
      backgroundColor: props.backgroundColor,
      backdropFilter: isMac ? 'none' : undefined,
    }"
    @dblclick="onTitlebarDblClick"
  >
    <!-- 左侧区域：mac 按钮组 或 Windows/Linux 图标占位 -->
    <div class="flex items-center justify-start" :class="leftAreaClass">
      <!-- macOS: 使用系统标题栏（hiddenInset），不自绘按钮，仅保留占位以平衡布局 -->
      <div v-if="isMac" class="h-full w-full" />

      <!-- Windows: 可选 ICO 图标 -->
      <div v-else-if="isWindows && showWindowsIcon && icon">
        <img :src="icon" alt="app icon" class="w-4 h-4 image-rendering-[optimize-contrast]" />
      </div>
      <div v-else />
    </div>

    <!-- 中间标题（居中显示）-->
    <div class="flex items-center justify-center text-center" :title="title">
      <slot name="title">
        <span class="text-[12.5px] tracking-[0.2px]" :style="{ color: titleColor }">{{
          title
        }}</span>
      </slot>
    </div>

    <!-- 右侧控制按钮（Windows/Linux 放在右侧；mac 这边留白以保证居中）-->
    <div class="flex items-center justify-end py-2 gap-1" :class="rightAreaClass">
      <template v-if="isWindows || isLinux">
        <!-- 最小化按钮 -->
        <button
          class="w-[36px] h-[20px] inline-flex items-center justify-center bg-transparent hover:bg-black/10 transition-colors [-webkit-app-region:no-drag]"
          @click="onMinimize"
          aria-label="Minimize"
        >
          <Minus class="w-4 h-4 text-white" />
        </button>

        <!-- 最大化按钮 -->
        <button
          class="w-[36px] h-[20px] inline-flex items-center justify-center bg-transparent hover:bg-black/10 transition-colors [-webkit-app-region:no-drag]"
          @click="onMaximize"
          aria-label="Maximize or Restore"
        >
          <component :is="isMaximized ? Copy : Square" class="w-4 h-4 text-white" />
        </button>
        <!-- 关闭按钮 -->
        <button
          class="w-[36px] h-[20px] inline-flex items-center justify-center bg-transparent hover:bg-red-500 hover:text-white transition-colors [-webkit-app-region:no-drag]"
          @click="onClose"
          aria-label="Close"
        >
          <X class="w-4 h-4 text-white" />
        </button>
      </template>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Minus, Square, Copy, X } from 'lucide-vue-next'

const props = withDefaults(
  defineProps<{
    title?: string
    icon?: string
    showWindowsIcon?: boolean
    height?: number
    /** macOS 下的标题栏高度（像素） */
    macHeight?: number
    /** macOS 左右占位宽度（像素） */
    macPlaceholderWidth?: number
    /** 标题栏背景色（优先级高于CSS变量），默认深灰 #3d3d3d */
    backgroundColor?: string
    /** 标题文字颜色 */
    titleColor?: string
  }>(),
  {
    title: '',
    icon: undefined,
    showWindowsIcon: true,
    height: 32,
    macHeight: 36,
    macPlaceholderWidth: 96,
    backgroundColor: '#3d3d3d',
    titleColor: '#ffffff',
  }
)

function detectPlatform(): string | undefined {
  const p = (window as any).electronAPI?.platform as string | undefined
  if (p) return p
  const ua = navigator.userAgent || ''
  if (/Macintosh|Mac OS X/i.test(ua)) return 'darwin'
  if (/Windows/i.test(ua)) return 'win32'
  if (/Linux/i.test(ua)) return 'linux'
  return undefined
}
const platform = detectPlatform()
const isWindows = computed(() => platform === 'win32')
const isMac = computed(() => platform === 'darwin')
const isLinux = computed(() => platform === 'linux')

const isMaximized = ref(false)

const onMinimize = async () => {
  await (window as any).electronAPI?.['window:minimize']?.()
}
const onMaximize = async () => {
  await (window as any).electronAPI?.['window:maximize']?.()
  // 简单切换本地状态（注意：如果用户手动调整窗口，可能与真实状态不同）
  isMaximized.value = !isMaximized.value
}
const onClose = async () => {
  await (window as any).electronAPI?.['window:close']?.()
}

onMounted(async () => {
  try {
    const state = await (window as any).electronAPI?.['window:get-state']?.()
    if (state && typeof state.isMaximized === 'boolean') {
      isMaximized.value = state.isMaximized
    }
  } catch {
    // 忽略：在非 Electron 环境或 API 不可用时
  }
})

const title = computed(() => props.title)

// 标题栏高度：mac 使用更贴近系统的 28px，其它平台使用 props.height
const headerHeight = computed(() => (isMac.value ? props.macHeight : props.height))
const headerHeightClass = computed(() => `h-[${headerHeight.value}px]`)

// 背景色类
// const backgroundClass = computed(() => {
//   const bgColor = props.backgroundColor
//   if (isMac.value) {
//     return `bg-[${bgColor}] backdrop-filter-none`
//   } else if (isWindows.value) {
//     return `bg-[${bgColor}]`
//   } else if (isLinux.value) {
//     return `bg-[${bgColor}]`
//   }
//   return `bg-[${bgColor}]`
// })

// 按钮高度类：确保按钮在标题栏中垂直居中
const buttonHeightClass = computed(() => {
  const height = headerHeight.value - 4
  return `h-[${height}px]`
})

// 使用 Tailwind 生成左右占位类，避免内联样式
const leftAreaClass = computed(() => {
  if (isMac.value) return `w-[${props.macPlaceholderWidth}px]`
  if (isWindows.value && props.showWindowsIcon && props.icon) return 'w-[28px]'
  return 'w-[8px]'
})
const rightAreaClass = computed(() => {
  if (isMac.value) return `w-[${props.macPlaceholderWidth}px]`
  return 'w-[120px]'
})

const onTitlebarDblClick = async (e: MouseEvent) => {
  // 双击标题栏：Windows/Linux 通常最大化/还原；macOS 默认也是双击缩放
  // 这里沿用已有 maximize API，实现“放大/还原”
  e.preventDefault()
  try {
    await onMaximize()
  } catch {
    // 忽略在非 Electron 环境的异常
  }
}
</script>

<style scoped>
.drag-region {
  -webkit-app-region: drag;
}
.no-drag {
  -webkit-app-region: no-drag;
}
</style>
