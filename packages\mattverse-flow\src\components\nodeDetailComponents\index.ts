import type { Component } from 'vue'

// 使用 Vite 的 import.meta.glob 动态导入所有 .vue 文件
const modules = import.meta.glob('./**/*.vue', { eager: true })

const components: { [key: string]: Component } = {}

// 处理所有模块
Object.entries(modules).forEach(([path, module]) => {
  // 移除开头的 './' 和结尾的 '.vue'
  const componentPath = path.replace(/^\.\/(.*)\.vue$/, '$1')

  // 分割路径获取模块名
  const pathParts = componentPath.split('/')

  // 1. 注册标准格式: folder-filename (例如: basicComponents-DataEntry)
  if (pathParts.length >= 2) {
    const folderName = pathParts[0]
    const fileName = pathParts[pathParts.length - 1]
    const standardName = `${folderName}-${fileName}`
    components[standardName] = (module as any).default
  }

  // 2. 注册类型格式: type (例如: DataEntry)
  // 这样在 getNodeDetailComponent 中可以直接通过 nodeData.data.type 查找
  if (pathParts.length >= 1) {
    const fileName = pathParts[pathParts.length - 1]
    components[fileName] = (module as any).default
  }

  // 3. 注册完整路径格式 (例如: basicComponents/DataEntry)
  components[componentPath.replace(/\//g, '-')] = (module as any).default
})

// 输出注册的组件列表，方便调试
// console.log('已注册的组件:', Object.keys(components))

export default components
