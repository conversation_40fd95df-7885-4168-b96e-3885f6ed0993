<template>
  <div class="simple-workflow-editor">
    <div class="header">
      <h2>工作流编辑器</h2>
      <div class="actions">
        <button @click="addSampleNode" class="btn btn-primary">添加示例节点</button>
        <button @click="clearAll" class="btn btn-secondary">清空</button>
        <button @click="executeWorkflow" class="btn btn-success" :disabled="nodes.length === 0">
          执行工作流
        </button>
      </div>
    </div>

    <div class="content">
      <div class="sidebar">
        <h3>节点面板</h3>
        <div class="node-types">
          <div
            v-for="nodeType in nodeTypes"
            :key="nodeType.type"
            class="node-type"
            @click="addNode(nodeType)"
          >
            <span class="icon">{{ nodeType.icon }}</span>
            <span>{{ nodeType.label }}</span>
          </div>
        </div>

        <div v-if="selectedNode" class="properties">
          <h3>节点属性</h3>
          <div class="property-group">
            <label>标题:</label>
            <input v-model="selectedNode.data.label" @input="updateSelectedNode" class="input" />
          </div>
          <div class="property-group">
            <label>描述:</label>
            <textarea
              v-model="selectedNode.data.description"
              @input="updateSelectedNode"
              class="textarea"
            ></textarea>
          </div>
        </div>
      </div>

      <div class="canvas-area">
        <FlowCanvas
          :show-background="true"
          :show-controls="true"
          :show-mini-map="false"
          @node-click="onNodeClick"
          @edge-click="onEdgeClick"
          @pane-click="onPaneClick"
        />
      </div>
    </div>

    <!-- 执行结果弹窗 -->
    <div v-if="showExecutionResult" class="modal-overlay" @click="closeExecutionResult">
      <div class="modal" @click.stop>
        <h3>执行结果</h3>
        <div v-if="executionResult">
          <p><strong>状态:</strong> {{ executionResult.success ? '成功' : '失败' }}</p>
          <p><strong>耗时:</strong> {{ executionResult.duration }}ms</p>
          <div v-if="executionResult.error" class="error">
            <strong>错误:</strong> {{ executionResult.error }}
          </div>
          <div class="logs">
            <h4>执行日志:</h4>
            <div v-for="log in executionResult.logs" :key="log.id" :class="`log-${log.level}`">
              [{{ formatTime(log.timestamp) }}] {{ log.message }}
            </div>
          </div>
        </div>
        <button @click="closeExecutionResult" class="btn btn-primary">关闭</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  useFlowStores,
  useFlowExecution,
  type WorkflowNode,
  type WorkflowEdge,
  type WorkflowExecutionResult,
} from '@mattverse/mattverse-flow'
import { FlowCanvas } from '@mattverse/mattverse-flow'

// 状态管理
const { flow, workflow } = useFlowStores()
const { executeCurrentWorkflow } = useFlowExecution()

// 响应式数据
const showExecutionResult = ref(false)
const executionResult = ref<WorkflowExecutionResult | null>(null)

// 计算属性
const nodes = computed(() => flow.nodes)
const edges = computed(() => flow.edges)
const selectedNode = computed(() => {
  const selectedIds = flow.selectedNodes
  if (selectedIds.length === 1) {
    return flow.nodes.find(n => n.id === selectedIds[0])
  }
  return null
})

// 节点类型
const nodeTypes = [
  { type: 'start', label: '开始', icon: '▶️' },
  { type: 'process', label: '处理', icon: '⚙️' },
  { type: 'decision', label: '判断', icon: '❓' },
  { type: 'end', label: '结束', icon: '⏹️' },
]

// 方法
const addNode = (nodeType: any) => {
  const randomX = Math.random() * 400 + 100
  const randomY = Math.random() * 300 + 100

  flow.addNode({
    type: nodeType.type,
    position: { x: randomX, y: randomY },
    data: {
      label: nodeType.label,
      description: `这是一个${nodeType.label}节点`,
    },
  })
}

const addSampleNode = () => {
  addNode(nodeTypes[Math.floor(Math.random() * nodeTypes.length)])
}

const clearAll = () => {
  flow.clearFlow()
}

const updateSelectedNode = () => {
  if (selectedNode.value) {
    flow.updateNode(selectedNode.value.id, {
      data: selectedNode.value.data,
    })
  }
}

const executeWorkflow = async () => {
  try {
    // 创建一个临时工作流用于执行
    const tempWorkflow = workflow.createWorkflow('临时工作流', {
      nodes: flow.nodes,
      edges: flow.edges,
    })

    workflow.setCurrentWorkflow(tempWorkflow.id)

    const result = await executeCurrentWorkflow()
    executionResult.value = result
    showExecutionResult.value = true
  } catch (error) {
    console.error('执行失败:', error)
    alert(`执行失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

const onNodeClick = (node: WorkflowNode, event: MouseEvent) => {
  flow.selectNode(node.id, event.ctrlKey || event.metaKey)
}

const onEdgeClick = (edge: WorkflowEdge, event: MouseEvent) => {
  flow.selectEdge(edge.id, event.ctrlKey || event.metaKey)
}

const onPaneClick = (event: MouseEvent) => {
  flow.clearSelection()
}

const closeExecutionResult = () => {
  showExecutionResult.value = false
  executionResult.value = null
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString()
}
</script>

<style scoped>
.simple-workflow-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: white;
  border-bottom: 1px solid #e5e5e5;
}

.header h2 {
  margin: 0;
  color: #374151;
}

.actions {
  display: flex;
  gap: 0.5rem;
}

.content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 300px;
  background: white;
  border-right: 1px solid #e5e5e5;
  padding: 1rem;
  overflow-y: auto;
}

.sidebar h3 {
  margin: 0 0 1rem 0;
  color: #374151;
  font-size: 1rem;
}

.node-types {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.node-type {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  border: 1px solid #e5e5e5;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.node-type:hover {
  background: #f3f4f6;
  border-color: #3b82f6;
}

.icon {
  font-size: 1.2rem;
}

.properties {
  border-top: 1px solid #e5e5e5;
  padding-top: 1rem;
}

.property-group {
  margin-bottom: 1rem;
}

.property-group label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: #374151;
}

.canvas-area {
  flex: 1;
  position: relative;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.25rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.btn-success {
  background: #10b981;
  color: white;
}

.btn-success:hover {
  background: #059669;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.input,
.textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

.input:focus,
.textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.textarea {
  resize: vertical;
  min-height: 60px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.modal h3 {
  margin: 0 0 1rem 0;
  color: #374151;
}

.error {
  color: #dc2626;
  background: #fef2f2;
  padding: 0.75rem;
  border-radius: 0.25rem;
  margin: 1rem 0;
}

.logs {
  margin: 1rem 0;
}

.logs h4 {
  margin: 0 0 0.5rem 0;
  color: #374151;
}

.log-info {
  color: #059669;
}

.log-warn {
  color: #d97706;
}

.log-error {
  color: #dc2626;
}

.log-debug {
  color: #6b7280;
}
</style>
