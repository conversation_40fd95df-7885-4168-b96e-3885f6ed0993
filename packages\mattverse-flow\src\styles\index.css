/* Flow 组件样式 - Tailwind CSS v4 */
@import 'tailwindcss';
@import '@mattverse/shared/styles/theme.css';

/* 确保 UI 组件样式正确应用 */
@layer components {
  /* 流程图相关样式 */
  .vue-flow {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
  
  .vue-flow__node {
    border-color: hsl(var(--border));
  }
  
  .vue-flow__edge {
    stroke: hsl(var(--muted-foreground));
  }
  
  /* 工具栏样式 */
  .workflow-toolbar {
    background-color: hsl(var(--card));
    border-color: hsl(var(--border));
  }
  
  /* 节点设置面板样式 */
  .node-settings-panel {
    background-color: hsl(var(--card));
    border-color: hsl(var(--border));
  }
}
