import { themeColorMap } from '@mattverse/shared'
import { computed } from 'vue'

/**
 * 节点主题管理 Hook
 * 用于管理节点的主题、颜色等视觉属性
 */
export function useNodeTheme(initialTheme?: string) {
  const theme = ref(initialTheme || 'city-light')
  // 当前主题
  const currentTheme = computed(() => theme.value)

  // 是否暗黑模式
  const isDarkMode = computed(() => currentTheme.value?.includes('dark'))

  // 背景色缓存，优化性能
  const backgroundColorCache = new Map()

  // 颜色前缀查找表
  const colorPrefixMap = {
    materialDesign: {
      Basic: 'material-basic',
      Compute: 'material-compute',
      Data: 'material-data',
      default: 'material-basic',
    },
    batterySimulation: {
      Basic: 'battery-basic',
      Compute: 'battery-compute',
      Data: 'battery-data',
      default: 'battery-basic',
    },
    default: 'material-basic',
  }

  /**
   * 简化的哈希函数
   * @param str 输入字符串
   * @returns 哈希值
   */
  const simpleHash = str => {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      hash = (hash << 5) - hash + str.charCodeAt(i)
      hash = hash & hash // 转换为32位整数
    }
    return Math.abs(hash)
  }

  /**
   * 获取节点背景色
   * @param nodeId 节点ID
   * @param nodeType 节点类型
   * @param nodeCategory 节点类别
   * @param customBgColor 自定义背景色
   * @returns 背景色
   */
  const getNodeBackgroundColor = (nodeId, nodeType, nodeCategory, customBgColor) => {
    // 如果有自定义背景色，优先使用
    if (customBgColor) {
      return customBgColor
    }

    // 检查缓存
    const cacheKey = `${nodeId}-${nodeType}-${nodeCategory}-${currentTheme.value}`
    if (backgroundColorCache.has(cacheKey)) {
      return backgroundColorCache.get(cacheKey)
    }

    // 使用查找表确定颜色键前缀
    let colorKeyPrefix = 'material-basic'

    if (nodeCategory) {
      const categoryMap = colorPrefixMap[nodeCategory]
      if (categoryMap) {
        colorKeyPrefix = categoryMap[nodeType] || categoryMap.default
      }
    }

    // 获取当前主题下该类型的颜色数组
    const themeColors = themeColorMap[currentTheme.value] || {}
    const colorArray = themeColors[colorKeyPrefix] || []

    if (colorArray.length === 0) {
      return '#f2f2f2' // 默认颜色
    }

    // 优化哈希计算
    const colorIndex = simpleHash(nodeId) % colorArray.length
    const color = colorArray[colorIndex] || '#f2f2f2'

    // 存入缓存
    backgroundColorCache.set(cacheKey, color)
    return color
  }

  /**
   * 清除背景色缓存
   */
  const clearBackgroundColorCache = () => {
    backgroundColorCache.clear()
  }

  /**
   * 批量更新节点背景色
   * @param nodes 节点数组
   * @returns 更新后的节点数组
   */
  const updateNodesBackground = nodes => {
    return nodes.map(node => {
      const nodeType = node.data.nodeType || node.data.type
      const nodeCategory = node.data.category || ''
      return {
        ...node,
        data: {
          ...node.data,
          backgroundColor: getNodeBackgroundColor(
            node.id,
            nodeType,
            nodeCategory,
            node.data.backgroundColor
          ),
        },
      }
    })
  }

  return {
    currentTheme,
    isDarkMode,
    getNodeBackgroundColor,
    clearBackgroundColorCache,
    updateNodesBackground,
    setTheme: (newTheme: string) => {
      theme.value = newTheme
    },
  }
}
