/* 导入 Tailwind CSS */
@import 'tailwindcss';
/* 导入主题文件 */
@import '@mattverse/shared/styles/theme.css';
/* 导入 UI 组件样式 */
@import '@mattverse/mattverse-ui/styles';

/* 动态字体设置支持 */
:root {
  --base-font-size: 14px;
}

/* 让根元素占满视口，并禁用页面级滚动，由布局内区域自行滚动 */
html,
body {
  height: 100%;
  overflow: hidden;
}
/* 确保 body 使用动态字体设置，并去掉默认 8px 外边距避免出现滚动条 */
body {
  font-family: var(--font-display) !important;
  font-size: var(--base-font-size) !important;
  margin: 0 !important;
}

/* 确保所有文本元素都能继承字体设置 */
* {
  font-family: inherit;
}

/* 为不同的文本大小提供相对单位支持 */
.text-xs {
  font-size: calc(var(--base-font-size) * 0.75);
}
.text-sm {
  font-size: calc(var(--base-font-size) * 0.875);
}
.text-base {
  font-size: var(--base-font-size);
}
.text-lg {
  font-size: calc(var(--base-font-size) * 1.125);
}
.text-xl {
  font-size: calc(var(--base-font-size) * 1.25);
}
.text-2xl {
  font-size: calc(var(--base-font-size) * 1.5);
}
.text-3xl {
  font-size: calc(var(--base-font-size) * 1.875);
}
.text-4xl {
  font-size: calc(var(--base-font-size) * 2.25);
}

/* 让 Sidebar 避让自定义标题栏（MattTitleBar） */
/* 不改动组件实现，通过全局样式轻量覆盖 fixed 定位的顶部值 */
#app [data-slot='sidebar'] > .fixed {
  top: var(--mv-titlebar-height, 36px) !important; /* 兼容 Windows(32) 与 macOS(36)；默认 36px */
  height: calc(100svh - var(--mv-titlebar-height, 36px)) !important; /* 修正底部裁剪 */
}

/* 防止 router-view 外层因 SidebarProvider 的 min-h-svh 造成溢出滚动 */
#app [data-slot='sidebar-wrapper'] {
  min-height: calc(100vh - var(--mv-titlebar-height, 36px));
  min-height: calc(100svh - var(--mv-titlebar-height, 36px)); /* 现代浏览器优先 */
}
