{"name": "@mattverse/mattverse-flow", "version": "1.1.0", "private": true, "description": "Workflow engine and flow visualization for Mattverse applications", "type": "module", "main": "./dist/index.cjs.js", "module": "./dist/index.es.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.es.js", "require": "./dist/index.cjs.js", "types": "./dist/index.d.ts"}, "./style.css": {"import": "./dist/style.css", "require": "./dist/style.css", "default": "./dist/style.css"}, "./src/*": "./src/*"}, "files": ["dist", "src"], "scripts": {"build": "vite build", "dev": "vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx,.vue", "lint:fix": "eslint src --ext .ts,.tsx,.vue --fix", "typecheck": "vue-tsc --noEmit"}, "dependencies": {"@mattverse/shared": "workspace:*", "@mattverse/mattverse-ui": "workspace:*", "@mattverse/configs": "workspace:*", "vue": "^3.5.0", "pinia": "^2.3.0", "@vue-flow/core": "^1.45.0", "@vue-flow/controls": "^1.1.2", "@vue-flow/minimap": "^1.5.0", "@vue-flow/background": "^1.3.0", "@vue-flow/node-resizer": "^1.4.0", "@vue-flow/node-toolbar": "^1.1.0", "zod": "^3.23.0"}, "devDependencies": {"@mattverse/configs": "workspace:*", "vite": "^5.4.0", "@vitejs/plugin-vue": "^5.0.0", "vue-tsc": "^2.0.0"}, "peerDependencies": {"vue": "^3.5.0", "pinia": "^2.3.0"}}