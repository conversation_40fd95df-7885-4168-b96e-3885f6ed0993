<template>
  <div class="space-y-6">
    <!-- 全局日志开关 -->
    <div class="flex items-center justify-between">
      <div class="space-y-0.5">
        <Label class="text-sm font-medium">{{ $t('settings.log_options.enable_logging') }}</Label>
        <p class="text-xs text-muted-foreground">
          {{ $t('settings.log_options.enable_logging_desc') }}
        </p>
      </div>
      <Switch
        :model-value="logSettings.enabled"
        @update:model-value="updateLog('enabled', $event)"
      />
    </div>

    <Separator />

    <!-- 基础配置 -->
    <div class="space-y-4">
      <Label class="text-lg font-medium">{{ $t('settings.log_options.basic_config') }}</Label>

      <!-- 日志级别 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.log_options.log_level') }}</Label>
        <Select
          :model-value="logSettings.logLevel"
          :disabled="!logSettings.enabled"
          @update:model-value="updateLog('logLevel', $event)"
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="error">{{ $t('settings.log_options.level_error') }}</SelectItem>
            <SelectItem value="warn">{{ $t('settings.log_options.level_warn') }}</SelectItem>
            <SelectItem value="info">{{ $t('settings.log_options.level_info') }}</SelectItem>
            <SelectItem value="debug">{{ $t('settings.log_options.level_debug') }}</SelectItem>
            <SelectItem value="verbose">{{ $t('settings.log_options.level_verbose') }}</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 控制台显示 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.log_options.show_in_console') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.log_options.show_in_console_desc') }}
          </p>
        </div>
        <Switch
          :model-value="logSettings.showInConsole"
          :disabled="!logSettings.enabled"
          @update:model-value="updateLog('showInConsole', $event)"
        />
      </div>
    </div>

    <Separator />

    <!-- 文件配置 -->
    <div class="space-y-4">
      <Label class="text-lg font-medium">{{ $t('settings.log_options.file_config') }}</Label>

      <!-- 日志路径 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.log_options.log_path') }}</Label>
        <div class="flex gap-2">
          <Input
            :model-value="displayLogPath"
            :disabled="!logSettings.enabled"
            readonly
            class="flex-1"
          />
          <Button
            variant="outline"
            size="sm"
            :disabled="!logSettings.enabled"
            @click="selectLogPath"
          >
            <MattIcon name="FolderOpen" class="mr-2 h-4 w-4" />
            {{ $t('common.browse') }}
          </Button>
          <Button
            variant="outline"
            size="sm"
            :disabled="!logSettings.enabled"
            @click="openLogFolder"
          >
            <MattIcon name="ExternalLink" class="mr-2 h-4 w-4" />
            {{ $t('settings.log_options.open_folder') }}
          </Button>
        </div>
      </div>

      <!-- 文件名模式 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.log_options.filename_pattern') }}</Label>
        <Input
          :model-value="logSettings.fileNamePattern"
          :disabled="!logSettings.enabled"
          @update:model-value="updateLog('fileNamePattern', $event)"
          placeholder="{process}-{date}.log"
        />
        <p class="text-xs text-muted-foreground">
          {{ $t('settings.log_options.filename_pattern_desc') }}
        </p>
      </div>

      <!-- 文件大小限制 -->
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <Label class="text-sm">{{ $t('settings.log_options.max_file_size') }}</Label>
          <span class="text-sm text-muted-foreground">{{ logSettings.maxFileSize }}MB</span>
        </div>
        <Slider
          :model-value="[logSettings.maxFileSize]"
          :max="100"
          :min="1"
          :step="1"
          :disabled="!logSettings.enabled"
          @update:model-value="updateLog('maxFileSize', $event[0])"
        />
      </div>

      <!-- 最大文件数 -->
      <div class="space-y-2">
        <div class="flex items-center justify-between">
          <Label class="text-sm">{{ $t('settings.log_options.max_files') }}</Label>
          <span class="text-sm text-muted-foreground">{{ logSettings.maxFiles }}</span>
        </div>
        <Slider
          :model-value="[logSettings.maxFiles]"
          :max="100"
          :min="5"
          :step="1"
          :disabled="!logSettings.enabled"
          @update:model-value="updateLog('maxFiles', $event[0])"
        />
      </div>
    </div>

    <Separator />

    <!-- 自动清理设置 -->
    <div class="space-y-4">
      <Label class="text-lg font-medium">{{ $t('settings.log_options.auto_cleanup') }}</Label>

      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.log_options.enable_auto_cleanup') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.log_options.auto_cleanup_desc') }}
          </p>
        </div>
        <Switch
          :model-value="logSettings.autoCleanup"
          :disabled="!logSettings.enabled"
          @update:model-value="updateLog('autoCleanup', $event)"
        />
      </div>

      <div v-if="logSettings.autoCleanup && logSettings.enabled" class="space-y-2">
        <div class="flex items-center justify-between">
          <Label class="text-sm">{{ $t('settings.log_options.cleanup_days') }}</Label>
          <span class="text-sm text-muted-foreground"
            >{{ logSettings.cleanupDays }} {{ $t('settings.log_options.days') }}</span
          >
        </div>
        <Slider
          :model-value="[logSettings.cleanupDays]"
          :max="90"
          :min="1"
          :step="1"
          @update:model-value="updateLog('cleanupDays', $event[0])"
        />
      </div>
    </div>

    <Separator />

    <!-- 高级选项 -->
    <div class="space-y-4">
      <Label class="text-lg font-medium">{{ $t('settings.log_options.advanced_options') }}</Label>

      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.log_options.enable_file_rotation') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.log_options.file_rotation_desc') }}
          </p>
        </div>
        <Switch
          :model-value="logSettings.enableFileRotation"
          :disabled="!logSettings.enabled"
          @update:model-value="updateLog('enableFileRotation', $event)"
        />
      </div>

      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.log_options.compress_old_logs') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.log_options.compress_logs_desc') }}
          </p>
        </div>
        <Switch
          :model-value="logSettings.compressOldLogs"
          :disabled="!logSettings.enabled"
          @update:model-value="updateLog('compressOldLogs', $event)"
        />
      </div>
    </div>

    <Separator />

    <!-- 日志操作 -->
    <div class="space-y-4">
      <Label class="text-lg font-medium">{{ $t('settings.log_options.log_actions') }}</Label>

      <div class="flex flex-wrap gap-2">
        <Button
          variant="outline"
          size="sm"
          :disabled="!logSettings.enabled"
          @click="refreshLogFiles"
        >
          <MattIcon name="RefreshCw" class="mr-2 h-4 w-4" />
          {{ $t('settings.log_options.refresh_logs') }}
        </Button>
        <Button variant="outline" size="sm" :disabled="!logSettings.enabled" @click="cleanupLogs">
          <MattIcon name="Trash2" class="mr-2 h-4 w-4" />
          {{ $t('settings.log_options.cleanup_now') }}
        </Button>
        <Button
          variant="destructive"
          size="sm"
          :disabled="!logSettings.enabled"
          @click="clearAllLogs"
        >
          <MattIcon name="Trash" class="mr-2 h-4 w-4" />
          {{ $t('settings.log_options.clear_all_logs') }}
        </Button>
      </div>
    </div>

    <!-- 日志文件列表 -->
    <div class="space-y-4">
      <div class="flex items-center justify-between">
        <Label class="text-lg font-medium">{{ $t('settings.log_options.log_files') }}</Label>
        <div class="text-sm text-muted-foreground">
          {{ $t('settings.log_options.total_files') }}: {{ logFiles.length }}
        </div>
      </div>

      <div v-if="logFiles.length === 0" class="text-center py-8 text-muted-foreground">
        <MattIcon name="FileText" class="mx-auto h-12 w-12 mb-2 opacity-50" />
        <p>{{ $t('settings.log_options.no_log_files') }}</p>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="file in logFiles"
          :key="file.name"
          class="group relative rounded-lg border p-4 hover:bg-muted/50 transition-colors"
        >
          <!-- 删除按钮 -->
          <Button
            variant="ghost"
            size="sm"
            class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity h-6 w-6 p-0"
            @click="deleteLogFile(file.name)"
          >
            <MattIcon name="X" class="h-3 w-3" />
          </Button>

          <div class="space-y-2">
            <div class="flex items-center gap-2">
              <MattIcon name="FileText" class="h-4 w-4 text-muted-foreground" />
              <span class="text-sm font-medium truncate">{{ file.name }}</span>
            </div>
            <div class="text-xs text-muted-foreground space-y-1">
              <div>{{ $t('settings.log_options.file_size') }}: {{ formatFileSize(file.size) }}</div>
              <div>{{ $t('settings.log_options.modified') }}: {{ formatDate(file.modified) }}</div>
            </div>
            <div class="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                class="h-6 text-xs"
                @click="openLogFile(file.path)"
              >
                {{ $t('settings.log_options.open') }}
              </Button>
              <Button
                variant="outline"
                size="sm"
                class="h-6 text-xs"
                @click="copyLogPath(file.path)"
              >
                {{ $t('settings.log_options.copy_path') }}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { useSettingsStore, type LogSettings } from '@/store'

const { t } = useI18n()
const settingsStore = useSettingsStore()

// 响应式数据
const logFiles = ref<
  Array<{
    name: string
    path: string
    size: number
    modified: string
  }>
>([])

// 计算属性
const logSettings = computed(() => settingsStore.logs)

const displayLogPath = computed(() => {
  return logSettings.value.logPath || t('settings.log_options.default_path')
})

// 方法
const updateLog = (key: keyof LogSettings, value: any) => {
  settingsStore.updateLogs({ [key]: value })

  // 同步更新到主进程
  syncLogSettings()
}

// 同步日志设置到主进程
const syncLogSettings = async () => {
  if (window.electronAPI?.logger?.updateSettings) {
    try {
      // 使用 JSON 序列化/反序列化来完全移除响应式代理
      const rawSettings = JSON.parse(JSON.stringify(logSettings.value))
      await window.electronAPI.logger.updateSettings(rawSettings)
    } catch (error) {
      console.error('同步日志设置失败:', error)
    }
  }
}

// 选择日志路径
const selectLogPath = async () => {
  if (window.electronAPI?.dialog?.selectFolder) {
    try {
      const result = await window.electronAPI.dialog.selectFolder()
      if (result) {
        updateLog('logPath', result)
      }
    } catch (error) {
      console.error('选择日志路径失败:', error)
    }
  }
}

// 打开日志文件夹
const openLogFolder = async () => {
  if (window.electronAPI?.logger?.openLogFolder) {
    try {
      await window.electronAPI.logger.openLogFolder()
    } catch (error) {
      console.error('打开日志文件夹失败:', error)
    }
  }
}

// 刷新日志文件列表
const refreshLogFiles = async () => {
  if (window.electronAPI?.logger?.getLogFiles) {
    try {
      const files = await window.electronAPI.logger.getLogFiles()
      logFiles.value = files
    } catch (error) {
      console.error('获取日志文件列表失败:', error)
      logFiles.value = []
    }
  }
}

// 清理日志
const cleanupLogs = async () => {
  if (window.electronAPI?.logger?.cleanup) {
    try {
      await window.electronAPI.logger.cleanup()
      await refreshLogFiles()
    } catch (error) {
      console.error('清理日志失败:', error)
    }
  }
}

// 清空所有日志
const clearAllLogs = async () => {
  if (window.electronAPI?.logger?.clearAll) {
    try {
      await window.electronAPI.logger.clearAll()
      await refreshLogFiles()
    } catch (error) {
      console.error('清空日志失败:', error)
    }
  }
}

// 删除日志文件
const deleteLogFile = async (fileName: string) => {
  if (window.electronAPI?.logger?.deleteFile) {
    try {
      await window.electronAPI.logger.deleteFile(fileName)
      await refreshLogFiles()
    } catch (error) {
      console.error('删除日志文件失败:', error)
    }
  }
}

// 打开日志文件
const openLogFile = async (filePath: string) => {
  if (window.electronAPI?.logger?.openFile) {
    try {
      await window.electronAPI.logger.openFile(filePath)
    } catch (error) {
      console.error('打开日志文件失败:', error)
    }
  }
}

// 复制日志路径
const copyLogPath = async (filePath: string) => {
  try {
    await navigator.clipboard.writeText(filePath)
    // 这里可以显示一个成功提示
  } catch (error) {
    console.error('复制路径失败:', error)
  }
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleString()
}

// 生命周期
onMounted(async () => {
  await refreshLogFiles()

  // 如果日志路径为空，获取默认路径
  if (!logSettings.value.logPath && window.electronAPI?.logger?.getLogPath) {
    try {
      const defaultPath = await window.electronAPI.logger.getLogPath()
      updateLog('logPath', defaultPath)
    } catch (error) {
      console.error('获取默认日志路径失败:', error)
    }
  }

  // 同步当前设置到主进程
  syncLogSettings()
})
</script>
