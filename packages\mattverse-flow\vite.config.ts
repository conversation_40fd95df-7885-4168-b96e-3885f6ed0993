import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { autoImportPresets } from '@mattverse/configs'
export default defineConfig({
  plugins: [vue(), ...autoImportPresets.default('mattverse-flow', 'src/auto-imports.d.ts')],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@stores': resolve(__dirname, 'src/stores'),
      '@composables': resolve(__dirname, 'src/composables'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@types': resolve(__dirname, 'src/types'),
      '@nodes': resolve(__dirname, 'src/nodes'),
      '@core': resolve(__dirname, 'src/core'),
      '@mattverse/shared': resolve(__dirname, '../shared/src'),
      '@mattverse/mattverse-ui': resolve(__dirname, '../mattverse-ui/src'),
      '@mattverse/configs': resolve(__dirname, '../configs/src'),
    },
  },
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.ts'),
      name: 'MattverseFlow',
      fileName: format => `index.${format}.js`,
      formats: ['es', 'cjs'],
    },
    rollupOptions: {
      external: [
        'vue',
        'pinia',
        '@vue-flow/core',
        '@vue-flow/controls',
        '@vue-flow/minimap',
        '@vue-flow/background',
        '@vue-flow/node-resizer',
        '@vue-flow/node-toolbar',
        'zod',
        '@mattverse/shared',
        '@mattverse/configs',
        '@mattverse/mattverse-ui',
      ],
      output: {
        globals: {
          vue: 'Vue',
          pinia: 'Pinia',
          '@vue-flow/core': 'VueFlowCore',
          '@vue-flow/controls': 'VueFlowControls',
          '@vue-flow/minimap': 'VueFlowMinimap',
          '@vue-flow/background': 'VueFlowBackground',
          '@vue-flow/node-resizer': 'VueFlowNodeResizer',
          '@vue-flow/node-toolbar': 'VueFlowNodeToolbar',
          zod: 'Zod',
        },
        assetFileNames: assetInfo => {
          if (assetInfo.name?.endsWith('.css')) {
            return 'style.css'
          }
          return assetInfo.name || 'assets/[name].[ext]'
        },
      },
    },
    cssCodeSplit: true,
    sourcemap: true,
  },
})
