/**
 * 缓存管理服务
 * 负责自动清理和退出时清理缓存
 */
import { app } from 'electron'
import { logger } from '../utils/logger'
import { cacheHandlers, type CacheType } from '../handlers/cache'

/**
 * 缓存设置接口
 */
export interface CacheManagerSettings {
  enableCache: boolean
  clearOnExit: boolean
  autoClean: {
    enabled: boolean
    interval: 'daily' | 'weekly' | 'monthly' | 'never'
    maxAge: number
  }
  types: Record<CacheType, { enabled: boolean; maxSize: number; autoClean: boolean }>
}

/**
 * 缓存管理器类
 */
export class CacheManager {
  private settings: CacheManagerSettings | null = null
  private autoCleanTimer: NodeJS.Timeout | null = null
  private isInitialized = false

  /**
   * 初始化缓存管理器
   */
  initialize(settings: CacheManagerSettings): void {
    if (this.isInitialized) {
      logger.warn('缓存管理器已经初始化')
      return
    }

    this.settings = settings
    this.setupAutoClean()
    this.setupExitHandler()
    this.isInitialized = true

    logger.info('缓存管理器初始化完成', {
      enableCache: settings.enableCache,
      clearOnExit: settings.clearOnExit,
      autoClean: settings.autoClean,
    })
  }

  /**
   * 更新设置
   */
  updateSettings(newSettings: CacheManagerSettings): void {
    const oldSettings = this.settings
    this.settings = newSettings

    // 如果自动清理设置发生变化，重新设置定时器
    if (
      !oldSettings ||
      oldSettings.autoClean.enabled !== newSettings.autoClean.enabled ||
      oldSettings.autoClean.interval !== newSettings.autoClean.interval
    ) {
      this.setupAutoClean()
    }

    logger.info('缓存管理器设置已更新')
  }

  /**
   * 设置自动清理定时器
   */
  private setupAutoClean(): void {
    // 清除现有定时器
    if (this.autoCleanTimer) {
      clearInterval(this.autoCleanTimer)
      this.autoCleanTimer = null
    }

    if (!this.settings?.enableCache || !this.settings.autoClean.enabled) {
      logger.info('自动清理已禁用')
      return
    }

    const { interval } = this.settings.autoClean

    if (interval === 'never') {
      logger.info('自动清理间隔设置为从不执行')
      return
    }

    // 计算间隔时间（毫秒）
    const intervalMs = this.getIntervalMs(interval)

    // 设置定时器
    this.autoCleanTimer = setInterval(() => {
      this.executeAutoClean()
    }, intervalMs)

    logger.info(`自动清理定时器已设置，间隔: ${interval} (${intervalMs}ms)`)
  }

  /**
   * 获取间隔时间（毫秒）
   */
  private getIntervalMs(interval: 'daily' | 'weekly' | 'monthly'): number {
    switch (interval) {
      case 'daily':
        return 24 * 60 * 60 * 1000 // 24小时
      case 'weekly':
        return 7 * 24 * 60 * 60 * 1000 // 7天
      case 'monthly':
        return 30 * 24 * 60 * 60 * 1000 // 30天
      default:
        return 24 * 60 * 60 * 1000 // 默认24小时
    }
  }

  /**
   * 执行自动清理
   */
  private async executeAutoClean(): Promise<void> {
    if (!this.settings?.enableCache || !this.settings.autoClean.enabled) {
      return
    }

    try {
      // 获取启用自动清理的缓存类型
      const enabledTypes = Object.entries(this.settings.types)
        .filter(([, config]) => config.enabled && config.autoClean)
        .map(([type]) => type as CacheType)

      if (enabledTypes.length === 0) {
        logger.info('没有启用自动清理的缓存类型')
        return
      }

      logger.info('开始执行自动缓存清理', { enabledTypes })

      const result = await cacheHandlers['cache:auto-clean']({
        enabled: this.settings.autoClean.enabled,
        interval: this.settings.autoClean.interval,
        maxAge: this.settings.autoClean.maxAge,
        enabledTypes,
      })

      if (result.success) {
        logger.info('自动缓存清理完成', { cleaned: result.cleaned })
      } else {
        logger.error('自动缓存清理失败')
      }
    } catch (error) {
      logger.error('执行自动缓存清理时出错:', error)
    }
  }

  /**
   * 设置应用退出处理器
   */
  private setupExitHandler(): void {
    // 监听应用退出事件
    app.on('before-quit', async event => {
      if (!this.settings?.enableCache || !this.settings.clearOnExit) {
        return
      }

      // 阻止默认退出行为，先执行清理
      event.preventDefault()

      try {
        await this.executeExitClean()
      } catch (error) {
        logger.error('退出时清理缓存失败:', error)
      } finally {
        // 清理完成后真正退出应用
        app.exit()
      }
    })

    logger.info('应用退出处理器已设置')
  }

  /**
   * 执行退出时清理
   */
  private async executeExitClean(): Promise<void> {
    if (!this.settings?.enableCache || !this.settings.clearOnExit) {
      return
    }

    try {
      // 获取启用的缓存类型
      const enabledTypes = Object.entries(this.settings.types)
        .filter(([, config]) => config.enabled)
        .map(([type]) => type as CacheType)

      if (enabledTypes.length === 0) {
        logger.info('没有启用的缓存类型需要清理')
        return
      }

      logger.info('开始执行退出时缓存清理', { enabledTypes })

      const result = await cacheHandlers['cache:clear-on-exit'](enabledTypes)

      if (result.success) {
        logger.info('退出时缓存清理完成')
      } else {
        logger.error('退出时缓存清理失败')
      }
    } catch (error) {
      logger.error('执行退出时缓存清理时出错:', error)
    }
  }

  /**
   * 手动触发自动清理（用于测试）
   */
  async triggerAutoClean(): Promise<void> {
    await this.executeAutoClean()
  }

  /**
   * 获取当前设置
   */
  getSettings(): CacheManagerSettings | null {
    return this.settings
  }

  /**
   * 销毁缓存管理器
   */
  destroy(): void {
    if (this.autoCleanTimer) {
      clearInterval(this.autoCleanTimer)
      this.autoCleanTimer = null
    }
    this.isInitialized = false
    logger.info('缓存管理器已销毁')
  }
}

// 导出单例实例
export const cacheManager = new CacheManager()
