<template>
  <Card
    class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
  >
    <CardHeader>
      <CardTitle class="flex items-center space-x-2">
        <MattIcon name="Edit" class="h-5 w-5" />
        <span>{{ $t('settings.flow.editor.title') }}</span>
      </CardTitle>
      <CardDescription>
        {{ $t('settings.flow.editor.description') }}
      </CardDescription>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- 连接模式 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.editor.connection_mode') }}</Label>
        <Select
          :model-value="flowSettings.connectionMode"
          @update:model-value="updateFlowSettings('connectionMode', $event)"
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="mode in connectionModes"
              :key="mode.value"
              :value="mode.value"
            >
              {{ mode.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Separator />

      <!-- 验证级别 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.editor.validation_level') }}</Label>
        <Select
          :model-value="flowSettings.validationLevel"
          @update:model-value="updateFlowSettings('validationLevel', $event)"
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="level in validationLevels"
              :key="level.value"
              :value="level.value"
            >
              {{ level.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Separator />

      <!-- 布局方向 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.editor.layout_direction') }}</Label>
        <Select
          :model-value="flowSettings.layoutDirection"
          @update:model-value="updateFlowSettings('layoutDirection', $event)"
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="direction in layoutDirections"
              :key="direction.value"
              :value="direction.value"
            >
              {{ direction.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from '@mattverse/i18n'
import type { NodeConnectionMode, FlowValidationLevel, FlowLayoutDirection, FlowSettingsState } from '@/store'

interface Props {
  flowSettings: FlowSettingsState
  updateFlowSettings: (key: keyof FlowSettingsState, value: any) => void
}

defineProps<Props>()

const { t } = useI18n()

const connectionModes = computed(() => [
  { value: 'auto' as NodeConnectionMode, label: t('settings.flow.editor.connection_modes.auto') },
  { value: 'manual' as NodeConnectionMode, label: t('settings.flow.editor.connection_modes.manual') },
  { value: 'smart' as NodeConnectionMode, label: t('settings.flow.editor.connection_modes.smart') },
])

const validationLevels = computed(() => [
  { value: 'none' as FlowValidationLevel, label: t('settings.flow.editor.validation_levels.none') },
  { value: 'basic' as FlowValidationLevel, label: t('settings.flow.editor.validation_levels.basic') },
  { value: 'strict' as FlowValidationLevel, label: t('settings.flow.editor.validation_levels.strict') },
])

const layoutDirections = computed(() => [
  { value: 'horizontal' as FlowLayoutDirection, label: t('settings.flow.editor.layout_directions.horizontal') },
  { value: 'vertical' as FlowLayoutDirection, label: t('settings.flow.editor.layout_directions.vertical') },
  { value: 'auto' as FlowLayoutDirection, label: t('settings.flow.editor.layout_directions.auto') },
])
</script>
