/**
 * Electron 日志管理处理器
 */
import { shell } from 'electron'
import { promises as fs } from 'fs'
import { join } from 'path'
import { logger } from '../utils/logger'

/**
 * 日志文件信息接口
 */
export interface LogFileInfo {
  name: string
  path: string
  size: number
  modified: string
}

/**
 * 日志设置接口
 */
export interface LoggerSettings {
  enabled: boolean
  logLevel: 'error' | 'warn' | 'info' | 'debug' | 'verbose'
  showInConsole: boolean
  logPath: string
  fileNamePattern: string
  maxFileSize: number
  maxFiles: number
  autoCleanup: boolean
  cleanupDays: number
  enableFileRotation: boolean
  compressOldLogs: boolean
}

/**
 * 获取日志文件信息
 */
async function getLogFileInfo(filePath: string): Promise<LogFileInfo | null> {
  try {
    const stats = await fs.stat(filePath)
    const name = filePath.split('/').pop() || filePath.split('\\').pop() || 'unknown'

    return {
      name,
      path: filePath,
      size: stats.size,
      modified: stats.mtime.toISOString(),
    }
  } catch (error) {
    logger.error('获取日志文件信息失败:', error)
    return null
  }
}

/**
 * 删除日志文件
 */
async function deleteLogFile(filePath: string): Promise<boolean> {
  try {
    await fs.unlink(filePath)
    logger.info('删除日志文件成功:', filePath)
    return true
  } catch (error) {
    logger.error('删除日志文件失败:', error)
    return false
  }
}

/**
 * 清理过期日志文件
 */
async function cleanupOldLogFiles(logPath: string, maxDays: number): Promise<number> {
  try {
    const files = await fs.readdir(logPath)
    const now = Date.now()
    const maxAge = maxDays * 24 * 60 * 60 * 1000
    let cleanedCount = 0

    for (const file of files) {
      if (!file.endsWith('.log')) continue

      const filePath = join(logPath, file)
      try {
        const stats = await fs.stat(filePath)
        const fileAge = now - stats.mtime.getTime()

        if (fileAge > maxAge) {
          await fs.unlink(filePath)
          cleanedCount++
          logger.info('清理过期日志文件:', file)
        }
      } catch (error) {
        logger.warn('处理日志文件失败:', file, error)
      }
    }

    return cleanedCount
  } catch (error) {
    logger.error('清理日志文件失败:', error)
    return 0
  }
}

/**
 * 日志处理器
 */
export const loggerHandlers = {
  /**
   * 获取日志路径
   */
  'logger:get-path': async (): Promise<string> => {
    return logger.getLogPath()
  },

  /**
   * 获取日志文件列表
   */
  'logger:get-files': async (): Promise<LogFileInfo[]> => {
    try {
      const logPath = logger.getLogPath()
      const files = await fs.readdir(logPath)
      const logFiles: LogFileInfo[] = []

      for (const file of files) {
        if (file.endsWith('.log')) {
          const filePath = join(logPath, file)
          const fileInfo = await getLogFileInfo(filePath)
          if (fileInfo) {
            logFiles.push(fileInfo)
          }
        }
      }

      // 按修改时间排序，最新的在前
      logFiles.sort((a, b) => new Date(b.modified).getTime() - new Date(a.modified).getTime())

      return logFiles
    } catch (error) {
      logger.error('获取日志文件列表失败:', error)
      return []
    }
  },

  /**
   * 打开日志文件夹
   */
  'logger:open-folder': async (): Promise<{ success: boolean }> => {
    try {
      const logPath = logger.getLogPath()
      await shell.openPath(logPath)
      return { success: true }
    } catch (error) {
      logger.error('打开日志文件夹失败:', error)
      return { success: false }
    }
  },

  /**
   * 打开日志文件
   */
  'logger:open-file': async (filePath: string): Promise<{ success: boolean }> => {
    try {
      await shell.openPath(filePath)
      return { success: true }
    } catch (error) {
      logger.error('打开日志文件失败:', error)
      return { success: false }
    }
  },

  /**
   * 删除日志文件
   */
  'logger:delete-file': async (fileName: string): Promise<{ success: boolean }> => {
    try {
      const logPath = logger.getLogPath()
      const filePath = join(logPath, fileName)
      const success = await deleteLogFile(filePath)
      return { success }
    } catch (error) {
      logger.error('删除日志文件失败:', error)
      return { success: false }
    }
  },

  /**
   * 清理过期日志
   */
  'logger:cleanup': async (
    maxDays: number = 30
  ): Promise<{ success: boolean; cleanedCount: number }> => {
    try {
      const logPath = logger.getLogPath()
      const cleanedCount = await cleanupOldLogFiles(logPath, maxDays)
      return { success: true, cleanedCount }
    } catch (error) {
      logger.error('清理日志失败:', error)
      return { success: false, cleanedCount: 0 }
    }
  },

  /**
   * 清空所有日志
   */
  'logger:clear-all': async (): Promise<{ success: boolean; deletedCount: number }> => {
    try {
      const logPath = logger.getLogPath()
      const files = await fs.readdir(logPath)
      let deletedCount = 0

      for (const file of files) {
        if (file.endsWith('.log')) {
          const filePath = join(logPath, file)
          const success = await deleteLogFile(filePath)
          if (success) {
            deletedCount++
          }
        }
      }

      return { success: true, deletedCount }
    } catch (error) {
      logger.error('清空日志失败:', error)
      return { success: false, deletedCount: 0 }
    }
  },

  /**
   * 更新日志设置
   */
  'logger:update-settings': async (settings: LoggerSettings): Promise<{ success: boolean }> => {
    try {
      logger.info('更新日志设置:', settings)

      // 这里可以根据设置更新日志配置
      // 例如更新日志级别、文件路径等

      return { success: true }
    } catch (error) {
      logger.error('更新日志设置失败:', error)
      return { success: false }
    }
  },

  /**
   * 获取日志统计信息
   */
  'logger:get-stats': async (): Promise<{
    totalFiles: number
    totalSize: number
    oldestFile: string | null
    newestFile: string | null
  }> => {
    return logger.getLogStats()
  },
}
