/**
 * Electron 应用工厂
 * 提供统一的应用创建和配置
 */
import { app, BrowserWindow } from 'electron'
import { join } from 'node:path'
import { IPCHandlerFactory } from '../handlers/factory'
import { IPCChannelHandler } from '../types/ipc'
import { logger } from '../utils/logger'
import { createDevToolsManager, type DevToolsConfig } from '../utils/devtools'

export interface ElectronAppConfig {
  window?: {
    width?: number
    height?: number
    minWidth?: number
    minHeight?: number
    show?: boolean
    autoHideMenuBar?: boolean
    icon?: string
    frame?: boolean
    transparent?: boolean
    title?: string
    /** macOS 专用：'default' | 'hidden' | 'hiddenInset' | 'customButtonsOnHover' */
    titleBarStyle?: 'default' | 'hidden' | 'hiddenInset' | 'customButtonsOnHover'
    webPreferences?: {
      preload?: string
      sandbox?: boolean
      contextIsolation?: boolean
      nodeIntegration?: boolean
    }
  }
  handlers?: Record<string, IPCChannelHandler>
  devTools?: DevToolsConfig
  onReady?: () => void
  onWindowCreated?: (window: BrowserWindow) => void
}

export function createElectronApp(config: ElectronAppConfig = {}) {
  const { window: windowConfig = {}, handlers = {}, devTools, onReady, onWindowCreated } = config

  let mainWindow: BrowserWindow | null = null

  // 创建开发者工具管理器
  const devToolsManager = createDevToolsManager(devTools)

  function createWindow(): BrowserWindow {
    // 默认窗口配置
    const defaultConfig = {
      width: 1200,
      height: 800,
      show: false,
      autoHideMenuBar: true,
      // transparent: false,
      // backgroundColor: '#1a1a1a', // 设置背景色，避免白屏闪烁
      webPreferences: {
        preload: join(__dirname, '../preload/index.js'),
        sandbox: false,
        contextIsolation: true,
        nodeIntegration: false,
      },
    }

    // 合并配置
    const finalConfig = {
      ...defaultConfig,
      ...windowConfig,
      webPreferences: {
        ...defaultConfig.webPreferences,
        ...windowConfig.webPreferences,
      },
    }

    // 创建浏览器窗口
    mainWindow = new BrowserWindow(finalConfig)

    // 窗口准备就绪时的处理
    mainWindow.on('ready-to-show', () => {
      // 延迟设置最大化：仅在 Windows/Linux 下启用，macOS 保持可拖拽体验
      setTimeout(() => {
        if (mainWindow && !mainWindow.isDestroyed()) {
          if (process.platform !== 'darwin') {
            mainWindow.maximize()
          }
        }
      }, 200)

      logger.info('Main window is ready to show')
    })

    // 设置开发者工具
    devToolsManager.setupForWindow(mainWindow)

    // 添加键盘快捷键支持
    mainWindow.webContents.on('before-input-event', (_, input) => {
      if (!mainWindow || mainWindow.isDestroyed()) return

      // ESC 键退出全屏
      if (input.key === 'Escape' && mainWindow.isFullScreen()) {
        mainWindow.setFullScreen(false)
        logger.info('Exited fullscreen mode via ESC key')
      }
      // F11 键切换全屏
      if (input.key === 'F11') {
        const isFullScreen = mainWindow.isFullScreen()
        mainWindow.setFullScreen(!isFullScreen)
        logger.info(`Toggled fullscreen mode: ${!isFullScreen}`)
      }
    })

    // 加载页面
    if (process.env.NODE_ENV === 'development' && process.env['ELECTRON_RENDERER_URL']) {
      mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
    } else {
      mainWindow.loadFile(join(__dirname, '../../out/renderer/index.html'))
    }

    // 调用回调
    onWindowCreated?.(mainWindow)

    return mainWindow
  }

  function setupIPC() {
    // 注册默认处理器
    const defaultHandlers = IPCHandlerFactory.getDefaultHandlers()
    IPCHandlerFactory.registerAll({
      ...defaultHandlers,
      ...handlers,
    })
  }

  return {
    start: async () => {
      // 设置 IPC 处理器
      setupIPC()

      // 当 Electron 完成初始化时
      await app.whenReady()

      // 安装开发者工具扩展
      await devToolsManager.installExtensions()

      // 创建窗口
      createWindow()

      // 调用就绪回调
      onReady?.()

      // macOS 激活处理
      app.on('activate', function () {
        if (BrowserWindow.getAllWindows().length === 0) {
          createWindow()
        }
      })

      // Windows/Linux 退出处理
      app.on('window-all-closed', () => {
        if (process.platform !== 'darwin') {
          app.quit()
        }
      })

      logger.info('Electron app started')
    },

    getMainWindow: () => mainWindow,

    quit: () => app.quit(),

    restart: () => {
      app.relaunch()
      app.exit()
    },
  }
}
