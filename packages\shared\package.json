{"name": "@mattverse/shared", "version": "1.1.0", "private": true, "description": "Shared utilities and types for Mattverse applications", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}, "./utils": {"import": "./dist/utils.js", "types": "./dist/utils.d.ts"}, "./types": {"import": "./dist/types.js", "types": "./dist/types.d.ts"}, "./constants": {"import": "./dist/constants.js", "types": "./dist/constants.d.ts"}, "./directives": {"import": "./dist/directives.js", "types": "./dist/directives.d.ts"}, "./database": {"import": "./dist/database.js", "types": "./dist/database.d.ts"}, "./styles/theme.css": "./src/styles/theme.css", "./styles/base.css": "./src/styles/base.css", "./assets/*": "./src/assets/*"}, "files": ["dist", "src/assets", "src/styles"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"dayjs": "^1.11.0", "nanoid": "^5.0.0", "zod": "^3.23.0"}, "peerDependencies": {}, "devDependencies": {"@mattverse/configs": "workspace:*"}}