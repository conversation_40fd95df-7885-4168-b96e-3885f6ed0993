<template>
  <Card
    class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
  >
    <CardHeader>
      <CardTitle class="flex items-center space-x-2">
        <MattIcon name="Layout" class="h-5 w-5" />
        <span>{{ $t('settings.flow.interface.title') }}</span>
      </CardTitle>
      <CardDescription>
        {{ $t('settings.flow.interface.description') }}
      </CardDescription>
    </CardHeader>
    <CardContent>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 显示小地图 -->
        <div class="flex items-center justify-between">
          <div class="space-y-0.5">
            <Label class="text-sm">{{ $t('settings.flow.interface.show_minimap') }}</Label>
            <p class="text-xs text-muted-foreground">
              {{ $t('settings.flow.interface.show_minimap_desc') }}
            </p>
          </div>
          <Switch
            :model-value="flowSettings.showMiniMap"
            @update:model-value="updateFlowSettings('showMiniMap', $event)"
          />
        </div>

        <!-- 显示左侧控制器 -->
        <div class="flex items-center justify-between">
          <div class="space-y-0.5">
            <Label class="text-sm">{{ $t('settings.flow.interface.show_left_controls') }}</Label>
            <p class="text-xs text-muted-foreground">
              {{ $t('settings.flow.interface.show_left_controls_desc') }}
            </p>
          </div>
          <Switch
            :model-value="flowSettings.showLeftControls"
            @update:model-value="updateFlowSettings('showLeftControls', $event)"
          />
        </div>

        <!-- 显示右侧控制器 -->
        <div class="flex items-center justify-between">
          <div class="space-y-0.5">
            <Label class="text-sm">{{ $t('settings.flow.interface.show_right_controls') }}</Label>
            <p class="text-xs text-muted-foreground">
              {{ $t('settings.flow.interface.show_right_controls_desc') }}
            </p>
          </div>
          <Switch
            :model-value="flowSettings.showRightControls"
            @update:model-value="updateFlowSettings('showRightControls', $event)"
          />
        </div>

        <!-- 启用动画 -->
        <div class="flex items-center justify-between">
          <div class="space-y-0.5">
            <Label class="text-sm">{{ $t('settings.flow.interface.enable_animations') }}</Label>
            <p class="text-xs text-muted-foreground">
              {{ $t('settings.flow.interface.enable_animations_desc') }}
            </p>
          </div>
          <Switch
            :model-value="flowSettings.animationConfig.enableDragAnimation"
            @update:model-value="updateAnimationConfig('enableDragAnimation', $event)"
          />
        </div>
      </div>

      <Separator class="my-6" />

      <!-- 小地图配置 -->
      <div class="space-y-4">
        <Label class="text-sm font-medium">{{ $t('settings.flow.interface.minimap_config') }}</Label>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 背景颜色 -->
          <div class="space-y-2">
            <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.interface.minimap_bg_color') }}</Label>
            <Input
              :model-value="flowSettings.miniMapConfig.backgroundColor"
              type="color"
              @update:model-value="updateMiniMapConfig('backgroundColor', $event)"
            />
          </div>

          <!-- 节点描边颜色 -->
          <div class="space-y-2">
            <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.interface.minimap_node_stroke_color') }}</Label>
            <Input
              :model-value="flowSettings.miniMapConfig.nodeStrokeColor"
              type="color"
              @update:model-value="updateMiniMapConfig('nodeStrokeColor', $event)"
            />
          </div>
        </div>

        <!-- 节点描边宽度 -->
        <div class="space-y-2">
          <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.interface.minimap_node_stroke_width') }}</Label>
          <div class="space-y-2">
            <Slider
              :model-value="[flowSettings.miniMapConfig.nodeStrokeWidth]"
              :max="5"
              :min="0"
              :step="0.5"
              @update:model-value="updateMiniMapConfig('nodeStrokeWidth', $event[0])"
            />
            <div class="flex justify-between text-xs text-muted-foreground">
              <span>0px</span>
              <span class="font-medium">{{ flowSettings.miniMapConfig.nodeStrokeWidth }}px</span>
              <span>5px</span>
            </div>
          </div>
        </div>
      </div>

      <Separator class="my-6" />

      <!-- 动画配置 -->
      <div class="space-y-4">
        <Label class="text-sm font-medium">{{ $t('settings.flow.interface.animation_config') }}</Label>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- AI 节点动画 -->
          <div class="flex items-center justify-between">
            <div class="space-y-0.5">
              <Label class="text-sm">{{ $t('settings.flow.interface.enable_ai_node_animation') }}</Label>
              <p class="text-xs text-muted-foreground">
                {{ $t('settings.flow.interface.enable_ai_node_animation_desc') }}
              </p>
            </div>
            <Switch
              :model-value="flowSettings.animationConfig.enableAINodeAnimation"
              @update:model-value="updateAnimationConfig('enableAINodeAnimation', $event)"
            />
          </div>

          <!-- 节点高亮 -->
          <div class="flex items-center justify-between">
            <div class="space-y-0.5">
              <Label class="text-sm">{{ $t('settings.flow.interface.enable_node_highlight') }}</Label>
              <p class="text-xs text-muted-foreground">
                {{ $t('settings.flow.interface.enable_node_highlight_desc') }}
              </p>
            </div>
            <Switch
              :model-value="flowSettings.animationConfig.enableNodeHighlight"
              @update:model-value="updateAnimationConfig('enableNodeHighlight', $event)"
            />
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import type { MiniMapConfig, AnimationConfig, FlowSettingsState } from '@/store'

interface Props {
  flowSettings: FlowSettingsState
  updateFlowSettings: (key: keyof FlowSettingsState, value: any) => void
  updateMiniMapConfig: (key: keyof MiniMapConfig, value: any) => void
  updateAnimationConfig: (key: keyof AnimationConfig, value: any) => void
}

defineProps<Props>()
</script>
