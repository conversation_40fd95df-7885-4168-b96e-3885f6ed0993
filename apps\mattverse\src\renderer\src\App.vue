<template>
  <div id="app" class="w-full h-screen flex flex-col overflow-hidden">
    <MattTitleBar :title="appTitle" :icon="winIcon" :show-windows-icon="true" />
    <div class="flex-1 min-h-0 overflow-hidden">
      <router-view />
    </div>
    <Toaster close-button rich-colors position="bottom-right" theme="light" />
  </div>
</template>

<script setup lang="ts">
import { Toaster, MattTitleBar } from '@mattverse/mattverse-ui'
import { computed } from 'vue'

const appTitle = computed(() => 'MattVerse 电池设计自动化平台')
const winIcon = '/images/icons/mattverse/mattverse.ico'

// 设置一个全局 CSS 变量，供覆盖样式读取
if (typeof window !== 'undefined') {
  const platform = (window as any).electronAPI?.platform ?? navigator.platform
  const isMac = /Mac|Darwin/.test(platform)
  document.documentElement.style.setProperty('--mv-titlebar-height', isMac ? '36px' : '32px')
}
</script>
