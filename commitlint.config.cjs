module.exports = {
  // 继承的规则
  extends: ['@commitlint/config-conventional'], // 继承自 @commitlint/config-conventional 规则集
  // @see: https://commitlint.js.org/#/reference-rules
  rules: {
    'subject-case': [0], // subject大小写不做校验

    // 类型枚举，git提交type必须是以下类型
    'type-enum': [
      2,
      'always',
      [
        'feat', // 新增功能
        'fix', // 修复缺陷
        'docs', // 文档变更
        'style', // 代码格式（不影响功能，例如空格、分号等格式修正）
        'refactor', // 代码重构（不包括 bug 修复、功能新增）
        'perf', // 性能优化
        'test', // 添加疏漏测试或已有测试改动
        'build', // 构建流程、外部依赖变更（如升级 npm 包、修改 webpack 配置等）
        'ci', // 修改 CI 配置、脚本
        'revert', // 回滚 commit
        'chore', // 对构建过程或辅助工具和库的更改（不影响源文件、测试用例）
      ],
    ],
  },
  prompt: {
    messages: {
      type: '选择你要提交的类型 :',
      scope: '选择一个提交范围（可选）:',
      customScope: '请输入自定义的提交范围 :',
      subject: '填写简短精炼的变更描述 :\n',
      body: '填写更加详细的变更描述（可选）。使用 "|" 换行 :\n',
      breaking: '列举非兼容性重大的变更（可选）。使用 "|" 换行 :\n',
      footerPrefixesSelect: '选择关联issue前缀（可选）:',
      customFooterPrefix: '输入自定义issue前缀 :',
      footer: '列举关联issue (可选) 例如: #31, #I3244 :\n',
      generatingByAI: '正在通过 AI 生成你的提交简短描述...',
      generatedSelectByAI: '选择一个 AI 生成的简短描述:',
      confirmCommit: '是否提交或修改commit ?',
    },
    // prettier-ignore
    types: [
      { value: "feat",     name: "特性(feat):     ✨  新增功能", emoji: "✨" },
      { value: "fix",      name: "修复(fix):     🐛  修复缺陷", emoji: "🐛" },
      { value: "docs",     name: "文档(docs):     📝  文档变更", emoji: "📝" },
      { value: "style",    name: "格式(style):     🎨  代码格式（不影响功能，例如空格、分号等格式修正）", emoji: "🎨" },
      { value: "refactor", name: "重构(refactor):     ♻️  代码重构（不包括 bug 修复、功能新增）", emoji: "♻️" },
      { value: "perf",     name: "性能(perf):     ⚡️  性能优化", emoji: "⚡️" },
      { value: "test",     name: "测试(test):     ✅  添加疏漏测试或已有测试改动", emoji: "✅"},
      { value: "build",    name: "构建(build):     📦️  构建流程、外部依赖变更（如升级 npm 包、修改 vite 配置等）", emoji: "📦️"},
      { value: "ci",       name: "集成(ci):     🎡  修改 CI 配置、脚本",  emoji: "🎡"},
      { value: "revert",   name: "回退(revert):     ⏪️  回滚 commit",emoji: "⏪️"},
      { value: "chore",    name: "其他(chore):     🔨  对构建过程或辅助工具和库的更改（不影响源文件、测试用例）", emoji: "🔨"},
    ],
    useEmoji: true, // 是否使用 Emoji
    emojiAlign: 'center', // Emoji 对齐方式
    useAI: false, // 是否使用 AI 生成提交描述
    aiNumber: 1, // AI 生成提交描述时返回的选项数
    themeColorCode: '', // 自定义主题颜色
    scopes: [], // 提交范围选项（自定义）
    allowCustomScopes: true, // 是否允许自定义提交范围
    allowEmptyScopes: true, // 是否允许空提交范围
    customScopesAlign: 'bottom', // 自定义范围选项的位置
    customScopesAlias: 'custom', // 自定义范围的别名
    emptyScopesAlias: 'empty', // 空范围的别名
    upperCaseSubject: false, // 是否强制要求 subject 使用大写
    markBreakingChangeMode: false, // 是否标记重大变更
    allowBreakingChanges: ['feat', 'fix'], // 哪些类型的提交可以包含重大变更
    breaklineNumber: 100, // 换行字符的最大长度
    breaklineChar: '|', // 换行分隔符
    skipQuestions: [], // 跳过的问题
    issuePrefixes: [{ value: 'closed', name: 'closed:   ISSUES has been processed' }], // 关联 issue 的前缀
    customIssuePrefixAlign: 'top', // 自定义 issue 前缀的位置
    emptyIssuePrefixAlias: 'skip', // 空 issue 前缀的别名
    customIssuePrefixAlias: 'custom', // 自定义 issue 前缀的别名
    allowCustomIssuePrefix: true, // 是否允许自定义 issue 前缀
    allowEmptyIssuePrefix: true, // 是否允许空的 issue 前缀
    confirmColorize: true, // 提交确认时是否高亮
    maxHeaderLength: Infinity, // 提交头部最大长度
    maxSubjectLength: Infinity, // 提交主题最大长度
    minSubjectLength: 0, // 提交主题最小长度
    scopeOverrides: undefined, // 覆盖提交范围
    defaultBody: '', // 默认提交体
    defaultIssues: '', // 默认关联的 issues
    defaultScope: '', // 默认范围
    defaultSubject: '', // 默认主题
  },
}
