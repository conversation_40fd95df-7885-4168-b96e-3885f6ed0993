/**
 * 日志相关 API - 公共模块
 */
import { ipcRenderer } from 'electron'

/**
 * 日志文件信息接口
 */
export interface LogFileInfo {
  name: string
  path: string
  size: number
  modified: string
}

/**
 * 日志设置接口
 */
export interface LoggerSettings {
  enabled: boolean
  logLevel: 'error' | 'warn' | 'info' | 'debug' | 'verbose'
  showInConsole: boolean
  logPath: string
  fileNamePattern: string
  maxFileSize: number
  maxFiles: number
  autoCleanup: boolean
  cleanupDays: number
  enableFileRotation: boolean
  compressOldLogs: boolean
}

/**
 * 日志统计信息接口
 */
export interface LogStats {
  totalFiles: number
  totalSize: number
  oldestFile: string | null
  newestFile: string | null
}

/**
 * 日志 API 工厂函数
 * 创建类型安全的日志 API
 */
export function createLoggerAPI() {
  return {
    /**
     * 记录信息日志
     */
    info: (message: string, meta?: any): Promise<void> =>
      ipcRenderer.invoke('logger:info', message, meta),

    /**
     * 记录警告日志
     */
    warn: (message: string, meta?: any): Promise<void> =>
      ipcRenderer.invoke('logger:warn', message, meta),

    /**
     * 记录错误日志
     */
    error: (message: string, error?: any, meta?: any): Promise<void> =>
      ipcRenderer.invoke('logger:error', message, error, meta),

    /**
     * 记录调试日志
     */
    debug: (message: string, meta?: any): Promise<void> =>
      ipcRenderer.invoke('logger:debug', message, meta),

    /**
     * 获取日志路径
     */
    getLogPath: (): Promise<string> =>
      ipcRenderer.invoke('logger:get-path'),

    /**
     * 获取日志文件列表
     */
    getLogFiles: (): Promise<LogFileInfo[]> =>
      ipcRenderer.invoke('logger:get-files'),

    /**
     * 打开日志文件夹
     */
    openLogFolder: (): Promise<{ success: boolean }> =>
      ipcRenderer.invoke('logger:open-folder'),

    /**
     * 打开日志文件
     */
    openFile: (filePath: string): Promise<{ success: boolean }> =>
      ipcRenderer.invoke('logger:open-file', filePath),

    /**
     * 删除日志文件
     */
    deleteFile: (fileName: string): Promise<{ success: boolean }> =>
      ipcRenderer.invoke('logger:delete-file', fileName),

    /**
     * 清理过期日志
     */
    cleanup: (maxDays?: number): Promise<{ success: boolean; cleanedCount: number }> =>
      ipcRenderer.invoke('logger:cleanup', maxDays),

    /**
     * 清空所有日志
     */
    clearAll: (): Promise<{ success: boolean; deletedCount: number }> =>
      ipcRenderer.invoke('logger:clear-all'),

    /**
     * 更新日志设置
     */
    updateSettings: (settings: LoggerSettings): Promise<{ success: boolean }> =>
      ipcRenderer.invoke('logger:update-settings', settings),

    /**
     * 获取日志统计信息
     */
    getStats: (): Promise<LogStats> =>
      ipcRenderer.invoke('logger:get-stats'),
  }
}

/**
 * 默认导出的日志 API 实例
 */
export const loggerAPI = createLoggerAPI()

/**
 * 日志 API 类型定义
 */
export type LoggerAPI = ReturnType<typeof createLoggerAPI>
