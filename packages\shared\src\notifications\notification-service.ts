/**
 * 统一通知服务
 * 基于 vue-sonner API 设计，整合应用内通知、桌面通知和声音通知
 */
import { notificationSounds, type NotificationSoundType } from './notification-sounds'

// 通知类型定义，参考 vue-sonner
export type NotificationType = 'default' | 'success' | 'error' | 'warning' | 'info' | 'loading'

// 通知位置定义，参考 vue-sonner
export type NotificationPosition =
  | 'top-left'
  | 'top-center'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-center'
  | 'bottom-right'

// 通知主题定义
export type NotificationTheme = 'light' | 'dark' | 'system'

// 通知动作按钮
export interface NotificationAction {
  label: string
  onClick: () => void
}

// 基础通知选项，参考 vue-sonner API
export interface BaseNotificationOptions {
  // 基础属性
  id?: string | number
  title?: string
  description?: string
  duration?: number
  position?: NotificationPosition

  // 样式相关
  style?: Record<string, string>
  class?: string
  descriptionClass?: string
  unstyled?: boolean

  // 功能相关
  action?: NotificationAction
  cancel?: NotificationAction
  dismissible?: boolean
  closeButton?: boolean

  // 回调函数
  onDismiss?: (toast: { id: string | number }) => void
  onAutoClose?: (toast: { id: string | number }) => void

  // 自定义属性
  icon?: string | any
  richColors?: boolean

  // 扩展属性 - 控制通知类型
  showToast?: boolean // 是否显示应用内通知
  showDesktop?: boolean // 是否显示桌面通知
  playSound?: boolean // 是否播放声音
}

// 桌面通知选项
export interface DesktopNotificationOptions {
  title: string
  body?: string
  icon?: string
  silent?: boolean
  urgency?: 'normal' | 'critical' | 'low'
  timeoutType?: 'default' | 'never'
  actions?: Array<{ type: 'button'; text: string }>
}

// 通知设置接口
export interface NotificationSettings {
  enableNotifications: boolean // 应用内通知
  desktopNotifications: boolean // 系统通知
  soundNotifications: boolean // 声音通知
}

// 通知服务接口
export interface INotificationService {
  // 基础方法
  show(message: string, options?: BaseNotificationOptions): Promise<string | number>
  success(message: string, options?: BaseNotificationOptions): Promise<string | number>
  error(message: string, options?: BaseNotificationOptions): Promise<string | number>
  warning(message: string, options?: BaseNotificationOptions): Promise<string | number>
  info(message: string, options?: BaseNotificationOptions): Promise<string | number>
  loading(message: string, options?: BaseNotificationOptions): Promise<string | number>

  // 控制方法
  dismiss(id?: string | number): void

  // 设置方法
  updateSettings(settings: Partial<NotificationSettings>): void
  getSettings(): NotificationSettings
}

// 抽象通知服务基类
export abstract class BaseNotificationService implements INotificationService {
  protected settings: NotificationSettings = {
    enableNotifications: true,
    desktopNotifications: false,
    soundNotifications: true,
  }

  constructor(initialSettings?: Partial<NotificationSettings>) {
    if (initialSettings) {
      this.updateSettings(initialSettings)
    }
  }

  // 抽象方法，需要子类实现
  abstract show(message: string, options?: BaseNotificationOptions): Promise<string | number>
  abstract dismiss(id?: string | number): void

  // 便捷方法
  async success(message: string, options?: BaseNotificationOptions): Promise<string | number> {
    return this.show(message, { ...options, type: 'success' } as any)
  }

  async error(message: string, options?: BaseNotificationOptions): Promise<string | number> {
    return this.show(message, { ...options, type: 'error' } as any)
  }

  async warning(message: string, options?: BaseNotificationOptions): Promise<string | number> {
    return this.show(message, { ...options, type: 'warning' } as any)
  }

  async info(message: string, options?: BaseNotificationOptions): Promise<string | number> {
    return this.show(message, { ...options, type: 'info' } as any)
  }

  async loading(message: string, options?: BaseNotificationOptions): Promise<string | number> {
    return this.show(message, { ...options, type: 'loading' } as any)
  }

  // 设置管理
  updateSettings(settings: Partial<NotificationSettings>): void {
    this.settings = { ...this.settings, ...settings }

    // 更新声音通知设置
    if (settings.soundNotifications !== undefined) {
      notificationSounds.setEnabled(settings.soundNotifications)
    }
  }

  getSettings(): NotificationSettings {
    return { ...this.settings }
  }

  // 受保护的辅助方法
  protected shouldShowToast(options?: BaseNotificationOptions): boolean {
    return options?.showToast !== false && this.settings.enableNotifications
  }

  protected shouldShowDesktop(options?: BaseNotificationOptions): boolean {
    return options?.showDesktop !== false && this.settings.desktopNotifications
  }

  protected shouldPlaySound(options?: BaseNotificationOptions): boolean {
    return options?.playSound !== false && this.settings.soundNotifications
  }

  protected playNotificationSound(type: NotificationType): void {
    if (!this.shouldPlaySound()) return

    try {
      const soundType = this.mapNotificationTypeToSound(type)
      notificationSounds.play(soundType)
    } catch (error) {
      console.warn('Failed to play notification sound:', error)
    }
  }

  private mapNotificationTypeToSound(type: NotificationType): NotificationSoundType {
    switch (type) {
      case 'success':
        return 'success'
      case 'error':
        return 'error'
      case 'warning':
        return 'warning'
      case 'info':
        return 'info'
      default:
        return 'default'
    }
  }
}

// 导出类型和基类
export { notificationSounds, type NotificationSoundType }
