<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="max-w-7xl max-h-[90vh] w-[90vw]">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <MattIcon name="Activity" class="h-5 w-5" />
          日志管理
        </DialogTitle>
        <DialogDescription> 查看和管理计算任务日志，支持搜索、排序和批量操作 </DialogDescription>
      </DialogHeader>

      <div class="dialog-body flex flex-col gap-4 p-4">
        <!-- 搜索表单区域 -->
        <div class="search-section">
          <MattSearchForm
            :fields="searchFields"
            :loading="loading"
            @search="handleSearch"
            @reset="handleReset"
          />
        </div>

        <!-- 表格区域 -->
        <div class="table-section flex-1 min-h-0">
          <MattTable
            :columns="tableColumns"
            :data="paginatedData"
            :loading="loading"
            :actions="tableActions"
            :show-column-settings="true"
            :row-selection="rowSelection"
            :row-key="record => record.taskId"
            class="h-full"
            :max-height="620"
            @selection-change="handleSelectionChange"
            @sort-change="handleSortChange"
          >
            <!-- 表格左上角插槽：选中信息 -->
            <template #header-left>
              <div class="flex items-center gap-4">
                <div
                  v-if="rowSelection.selectedRowKeys.length > 0"
                  class="text-sm text-muted-foreground"
                >
                  已选择 {{ rowSelection.selectedRowKeys.length }} 个任务
                </div>
              </div>
            </template>

            <!-- 表格右上角插槽：批量操作按钮 -->
            <template #header-right>
              <div class="flex items-center gap-2">
                <!-- 批量操作按钮 -->
                <template v-if="rowSelection.selectedRowKeys.length > 0">
                  <Button
                    v-for="action in batchActions"
                    :key="action.key"
                    :variant="action.variant"
                    size="sm"
                    class="h-9 gap-2"
                    @click="handleBatchAction(action.key)"
                  >
                    <MattIcon :name="action.icon" class="h-4 w-4" />
                    {{ action.label }}
                  </Button>
                </template>

                <!-- 刷新按钮 -->
                <Button
                  variant="outline"
                  size="sm"
                  class="h-9 gap-2"
                  @click="handleRefresh"
                  :disabled="loading"
                >
                  <MattIcon name="RefreshCw" class="h-4 w-4" />
                  刷新
                </Button>
              </div>
            </template>

            <!-- 服务名称列 -->
            <template #serviceName="{ record }">
              <div class="flex items-center gap-2">
                <span class="font-medium">{{ getServiceName(record.taskId) }}</span>
              </div>
            </template>

            <!-- 任务编号列 - 支持复制 -->
            <template #taskId="{ record }">
              <div class="flex items-center gap-2">
                <span class="font-mono text-xs">{{ record.taskId }}</span>
                <Button
                  variant="ghost"
                  size="icon"
                  class="h-6 w-6"
                  @click.stop="copyToClipboard(record.taskId)"
                >
                  <MattIcon name="Copy" class="h-3 w-3" />
                </Button>
              </div>
            </template>

            <!-- 运行状态列 -->
            <template #taskStatus="{ record }">
              <Badge :class="getStatusClass(record.taskStatus)">
                {{ getStatusText(record.taskStatus) }}
              </Badge>
            </template>

            <!-- 提交时间列 -->
            <template #createTime="{ record }">
              <span class="text-sm">{{ formatTimestamp(record.createTime) }}</span>
            </template>

            <!-- 运行时间列 -->
            <template #duration="{ record }">
              <span class="text-sm">{{ record.duration || '-' }}</span>
            </template>

            <!-- 进度列 -->
            <template #taskProcess="{ record }">
              <div class="flex items-center gap-2 min-w-[120px]">
                <Progress :model-value="getValidProgress(record.taskProcess)" class="flex-1" />
                <span class="text-xs text-muted-foreground whitespace-nowrap">
                  {{ (record.taskProcess || 0).toFixed(2) }}%
                </span>
              </div>
            </template>

            <!-- 操作列 -->
            <template #actions="{ record }">
              <div class="flex items-center gap-1">
                <!-- 详情按钮 -->
                <Button
                  variant="ghost"
                  size="sm"
                  class="h-8 w-8 p-0"
                  @click="handleViewDetails(record)"
                  title="查看详情"
                >
                  <MattIcon name="Eye" class="h-4 w-4" />
                </Button>

                <!-- 删除按钮 -->
                <Button
                  variant="ghost"
                  size="sm"
                  class="h-8 w-8 p-0 text-destructive hover:text-destructive"
                  @click="handleDeleteTask(record)"
                  title="删除任务"
                >
                  <MattIcon name="Trash2" class="h-4 w-4" />
                </Button>
              </div>
            </template>
          </MattTable>
        </div>

        <!-- 分页区域 -->
        <div class="pagination-section">
          <MattPagination
            v-model:current="currentPage"
            v-model:page-size="pageSize"
            :total="sortedData.length"
            :show-size-changer="true"
            :show-quick-jumper="true"
            @change="handlePageChange"
          />
        </div>
      </div>

      <!-- 删除确认弹框 -->
      <AlertDialog v-model:open="deleteDialogOpen">
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{{ deleteDialogContent.title }}</AlertDialogTitle>
            <AlertDialogDescription>
              {{ deleteDialogContent.description }}
              <br />
              <span class="text-muted-foreground text-sm">
                {{ deleteDialogContent.detail }}
              </span>
              <br />
              <span class="text-destructive text-sm font-medium">
                {{ deleteDialogContent.warning }}
              </span>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel @click="closeDeleteDialog">取消</AlertDialogCancel>
            <AlertDialogAction
              @click="confirmDeleteTask"
              class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              :disabled="deleteLoading"
            >
              <MattIcon v-if="deleteLoading" name="Loader2" class="mr-2 h-4 w-4 animate-spin" />
              {{ deleteDialogContent.confirmText }}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <!-- 任务详情弹框 -->
      <LogDetail v-model:open="detailDialogOpen" :task="selectedTask" />
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue'

import { useTaskStore } from '@/store'
import { logger, taskService, formatDate, type Task } from '@mattverse/shared'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  MattSearchForm,
  MattTable,
  MattPagination,
  type MattTableColumn,
  MattTableAction,
  MattSearchFormField,
  Button,
  Badge,
  Progress,
  Label,
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
  MattIcon,
} from '@mattverse/mattverse-ui'
import LogDetail from '@/components/dailog/LogDetail.vue'

// 使用 store
const taskStore = useTaskStore()

// Dialog 状态
const isOpen = ref(false)
const selectedTask = ref<Task | null>(null)

// 使用计算属性来控制详情弹框显示
const detailDialogOpen = computed({
  get: () => selectedTask.value !== null,
  set: (value: boolean) => {
    if (!value) {
      selectedTask.value = null
    }
  },
})

// 响应式数据
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)

// 删除相关状态
const deleteDialogOpen = ref(false)
const deleteLoading = ref(false)
const taskToDelete = ref<Task | null>(null)
const isBatchDelete = ref(false)

// 行选择配置
const rowSelection = reactive({
  type: 'checkbox' as const,
  selectedRowKeys: [] as (string | number)[],
  onChange: (keys: (string | number)[], rows: Task[]) => {
    logger.info('选中的任务变化', { count: keys.length, rowCount: rows.length, keys })
    // 同步更新选中状态
    rowSelection.selectedRowKeys = keys
  },
  getCheckboxProps: (record: Task) => {
    // 可以根据任务状态设置禁用条件
    return {
      disabled: record.taskStatus === 'Computing' || record.taskStatus === 'Initializing',
    }
  },
})

// 排序状态
const currentSortField = ref<string | null>(null)
const currentSortOrder = ref<'asc' | 'desc' | null>(null)

// 搜索表单数据
const searchForm = ref({
  keyword: '', // 搜索关键词（服务名称或任务编号）
  taskStatus: 'all', // 任务状态筛选
})

// 从任务ID中提取服务名称
const getServiceName = (taskId: string): string => {
  // 任务ID格式：0::checkInputBatteryData::6oDbcGeLrEGVyBZuIN0Vc0
  // 提取中间部分作为服务名称
  const parts = taskId.split('::')
  return parts.length >= 2 ? parts[1] : taskId
}

// 批量操作按钮配置
const batchActions = computed(() => [
  {
    key: 'delete',
    label: '批量删除',
    icon: 'Trash2',
    variant: 'destructive' as const,
  },
])

// 搜索表单字段配置
const searchFields = computed<MattSearchFormField[]>(() => [
  {
    name: 'keyword',
    type: 'input',
    label: '搜索',
    placeholder: '搜索服务名称或任务编号',
    icon: 'search',
    width: 'w-full sm:min-w-[280px] sm:max-w-[320px]',
  },
  {
    name: 'taskStatus',
    type: 'select',
    label: '运行状态',
    placeholder: '选择运行状态',
    options: [
      { label: '全部状态', value: 'all' },
      { label: '初始化中', value: 'Initializing' },
      { label: '计算中', value: 'Computing' },
      { label: '等待调度', value: 'Pending' },
      { label: '暂停', value: 'Paused' },
      { label: '任务完成', value: 'Finished' },
      { label: '任务失败', value: 'Error' },
      { label: '原始状态', value: 'TaskStay' },
      { label: '已终止', value: 'Abort' },
    ],
    width: 'w-full sm:min-w-[160px] sm:max-w-[200px]',
  },
])

// 表格列配置
const tableColumns = computed<MattTableColumn[]>(() => [
  {
    key: 'serviceName',
    title: '服务名称',
    width: 180,
    slot: true,
    align: 'center',
    hideable: false,
    defaultVisible: true,
  },
  {
    key: 'taskId',
    title: '任务编号',
    width: 200,
    slot: true,
    align: 'center',
    ellipsis: true,
    hideable: false,
    defaultVisible: true,
  },
  {
    key: 'createTime',
    title: '提交时间',
    width: 150,
    slot: true,
    align: 'center',
    sortable: true,
    hideable: true,
    defaultVisible: true,
  },
  {
    key: 'duration',
    title: '运行时间',
    width: 120,
    slot: true,
    align: 'center',
    hideable: true,
    defaultVisible: true,
  },
  {
    key: 'taskStatus',
    title: '运行状态',
    width: 120,
    slot: true,
    align: 'center',
    hideable: false,
    defaultVisible: true,
  },
  {
    key: 'taskProcess',
    title: '进度',
    width: 150,
    slot: true,
    align: 'center',
    hideable: false,
    defaultVisible: true,
  },
])

// 表格操作配置
const tableActions = computed<MattTableAction[]>(() => [])

// 计算属性
const filteredData = computed(() => {
  let data = taskStore.tasks

  // 关键词搜索（支持服务名称和任务ID）
  if (searchForm.value.keyword) {
    const keyword = searchForm.value.keyword.toLowerCase()
    data = data.filter(task => {
      const serviceName = getServiceName(task.taskId).toLowerCase()
      const taskId = task.taskId.toLowerCase()
      return serviceName.includes(keyword) || taskId.includes(keyword)
    })
  }

  // 任务状态筛选
  if (searchForm.value.taskStatus && searchForm.value.taskStatus !== 'all') {
    data = data.filter(task => task.taskStatus === searchForm.value.taskStatus)
  }

  return data
})

// 排序后的数据
const sortedData = computed(() => {
  const data = [...filteredData.value]

  // 如果有排序条件，进行排序
  if (currentSortField.value && currentSortOrder.value) {
    data.sort((a, b) => {
      let aValue: any = a[currentSortField.value as keyof Task]
      let bValue: any = b[currentSortField.value as keyof Task]

      // 处理时间字段的排序
      if (currentSortField.value === 'createTime') {
        aValue = Number(aValue) || 0
        bValue = Number(bValue) || 0
      }

      // 处理空值
      if (!aValue && !bValue) return 0
      if (!aValue) return 1
      if (!bValue) return -1

      // 比较值
      if (aValue < bValue) return currentSortOrder.value === 'asc' ? -1 : 1
      if (aValue > bValue) return currentSortOrder.value === 'asc' ? 1 : -1
      return 0
    })
  }

  return data
})

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return sortedData.value.slice(start, end)
})

// 删除弹框动态内容
const deleteDialogContent = computed(() => {
  if (isBatchDelete.value) {
    const selectedCount = rowSelection.selectedRowKeys.length
    return {
      title: '确认批量删除任务',
      description: `您确定要删除选中的 ${selectedCount} 个任务吗？`,
      detail: '此操作将删除所有选中的任务',
      warning: '此操作不可撤销，请谨慎操作！',
      confirmText: deleteLoading.value ? '删除中...' : '确认删除',
    }
  } else if (taskToDelete.value) {
    return {
      title: '确认删除任务',
      description: `您确定要删除任务 "${taskToDelete.value.taskId}" 吗？`,
      detail: `任务ID: ${taskToDelete.value.taskId}`,
      warning: '此操作不可撤销，请谨慎操作！',
      confirmText: deleteLoading.value ? '删除中...' : '确认删除',
    }
  }
  return {
    title: '确认删除',
    description: '',
    detail: '',
    warning: '',
    confirmText: '确认删除',
  }
})

// 工具方法
const { getStatusText, getStatusClass } = taskStore

// 获取有效的进度值（确保在0-100范围内）
const getValidProgress = (progress: number | undefined | null): number => {
  if (progress === null || progress === undefined || isNaN(progress)) {
    return 0
  }
  return Math.max(0, Math.min(100, progress))
}

// 格式化时间戳为日期时间字符串
const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp * 1000)
  return formatDate(date, 'YYYY/MM/DD HH:mm:ss')
}

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    await notify.success(`已复制: ${text}`)
  } catch (err) {
    logger.error('复制失败:', err)
    await notify.error('复制失败')
  }
}

// 事件处理方法
const handleSearch = (formData: Record<string, any>) => {
  searchForm.value = {
    keyword: formData.keyword || '',
    taskStatus: formData.taskStatus || 'all',
  }
  currentPage.value = 1
}

const handleReset = () => {
  searchForm.value = {
    keyword: '',
    taskStatus: 'all',
  }
  currentPage.value = 1
}

const handlePageChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
}

const handleRefresh = async () => {
  loading.value = true
  try {
    await taskStore.updateTaskList()
    await notify.success('任务列表已刷新')
  } catch (error) {
    logger.error('刷新任务列表失败:', error)
    await notify.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 查看详情
const handleViewDetails = (record: any) => {
  selectedTask.value = record as Task
  // 不需要手动设置 detailDialogOpen，计算属性会自动处理
}

// 删除任务
const handleDeleteTask = (record: any) => {
  taskToDelete.value = record as Task
  isBatchDelete.value = false
  deleteDialogOpen.value = true
}

// 选择变化处理方法
const handleSelectionChange = (keys: (string | number)[], rows: Task[]) => {
  logger.info('表格选择变化', { count: keys.length, rowCount: rows.length, keys })
  rowSelection.selectedRowKeys = keys
}

// 排序变化处理方法
const handleSortChange = (key: string, order: 'asc' | 'desc' | null) => {
  logger.info('表格排序变化', { field: key, order })
  currentSortField.value = order ? key : null
  currentSortOrder.value = order
  currentPage.value = 1
}

// 批量操作处理方法
const handleBatchAction = async (action: string) => {
  if (rowSelection.selectedRowKeys.length === 0) {
    await notify.warning('请先选择要操作的任务')
    return
  }

  if (action === 'delete') {
    isBatchDelete.value = true
    deleteDialogOpen.value = true
  }
}

// 删除相关方法
const closeDeleteDialog = () => {
  deleteDialogOpen.value = false
  taskToDelete.value = null
  isBatchDelete.value = false
  deleteLoading.value = false
}

const confirmDeleteTask = async () => {
  deleteLoading.value = true

  try {
    if (isBatchDelete.value) {
      // 批量删除
      const selectedCount = rowSelection.selectedRowKeys.length
      if (selectedCount === 0) {
        await notify.warning('请先选择要删除的任务')
        return
      }

      await notify.info(`正在批量删除 ${selectedCount} 个任务...`)

      const deletePromises = rowSelection.selectedRowKeys.map(key => {
        const task = taskStore.tasks.find(t => t.taskId === key)
        return task ? taskService.deleteTask(task.taskId) : Promise.resolve({ status: 'Success' })
      })

      await Promise.all(deletePromises)
      rowSelection.selectedRowKeys = []
      await taskStore.updateTaskList()

      if (paginatedData.value.length === 0 && currentPage.value > 1) {
        currentPage.value = currentPage.value - 1
      }

      await notify.success(`批量删除 ${selectedCount} 个任务成功`)
    } else {
      // 单个删除
      if (!taskToDelete.value) return

      const taskIdToDelete = taskToDelete.value.taskId
      const response = await taskService.deleteTask(taskIdToDelete)

      if (response.status === 'Success') {
        await notify.success(`删除任务成功: ${taskIdToDelete}`)
        await taskStore.updateTaskList()

        if (paginatedData.value.length === 0 && currentPage.value > 1) {
          currentPage.value = currentPage.value - 1
        }
      } else {
        throw new Error(response.message || '删除任务失败')
      }
    }

    closeDeleteDialog()
  } catch (error) {
    logger.error('删除任务失败:', error)
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    await notify.error(`删除任务失败: ${errorMessage}`)
  } finally {
    deleteLoading.value = false
  }
}

// Dialog 控制方法
const openDialog = async () => {
  isOpen.value = true
  // 打开时刷新数据
  await handleRefresh()
}

const closeDialog = () => {
  isOpen.value = false
  // 关闭时重置状态
  searchForm.value = {
    keyword: '',
    taskStatus: 'all',
  }
  currentPage.value = 1
  rowSelection.selectedRowKeys = []
}

// 暴露方法给父组件调用
defineExpose({
  openDialog,
  closeDialog,
})
</script>

<style scoped>
.dialog-body {
  max-height: 70vh;
  overflow-y: auto;
}

.search-section {
  flex-shrink: 0;
}

.table-section {
  flex: 1;
  min-height: 0;
}

.pagination-section {
  flex-shrink: 0;
}
</style>
