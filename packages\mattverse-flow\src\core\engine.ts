/**
 * 工作流引擎
 */
import { WorkflowExecutor } from './executor'
import { WorkflowValidator } from './validator'
import { WorkflowSerializer } from './serializer'
import type {
  Workflow,
  WorkflowExecutionContext,
  WorkflowExecutionResult,
  WorkflowValidationResult,
} from '../types'

export class WorkflowEngine {
  public readonly executor: WorkflowExecutor
  public readonly validator: WorkflowValidator
  public readonly serializer: WorkflowSerializer

  constructor() {
    this.executor = new WorkflowExecutor()
    this.validator = new WorkflowValidator()
    this.serializer = new WorkflowSerializer()
  }

  /**
   * 执行工作流
   */
  async executeWorkflow(
    workflowId: string,
    context: WorkflowExecutionContext = {}
  ): Promise<WorkflowExecutionResult> {
    return await this.executor.execute(workflowId, context)
  }

  /**
   * 验证工作流
   */
  validateWorkflow(workflow: Workflow): WorkflowValidationResult {
    return this.validator.validate(workflow)
  }

  /**
   * 序列化工作流
   */
  serializeWorkflow(workflow: Workflow): string {
    return this.serializer.serialize(workflow)
  }

  /**
   * 反序列化工作流
   */
  deserializeWorkflow(data: string): Workflow {
    return this.serializer.deserialize(data)
  }

  /**
   * 停止执行
   */
  stopExecution(executionId: string): boolean {
    return this.executor.stopExecution(executionId)
  }

  /**
   * 获取执行状态
   */
  getExecutionStatus(executionId: string) {
    return this.executor.getExecutionStatus(executionId)
  }
}
