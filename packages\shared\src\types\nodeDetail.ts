export interface NodeModule {
  name: string
  icon: string | { type: string; value: string }
  type: string
  description?: string
  sort?: number
  categories: Array<{
    name: string
    sort?: number
    nodes: Array<{
      id: string
      type: string
      data: {
        label: string
        type: string
        icon: { type: string; value: string }
        description: string
        category: string
        nodeType: string
        inputType: string[]
        outputType: string[]
        params: any
        sort?: number
      }
    }>
  }>
  enabled?: boolean
  isBuiltin?: boolean
}
