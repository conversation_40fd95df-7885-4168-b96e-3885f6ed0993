import { createRouter, createWebHashHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
  },
  {
    path: '/workflow',
    name: 'WorkflowEditor',
    component: () => import('../views/SimpleWorkflowEditor.vue'),
  },
  {
    path: '/workflow-advanced',
    name: 'WorkflowEditorAdvanced',
    component: () => import('../views/WorkflowEditor.vue'),
  },
  {
    path: '/grpc-test',
    name: 'GrpcTest',
    component: () => import('../views/GrpcTest.vue'),
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})

export default router
