<template>
  <div class="flex flex-col h-[calc(100vh-4rem)] overflow-hidden">
    <!-- 关于内容 - 居中显示 -->
    <div class="flex-1 min-h-0 overflow-hidden flex items-center justify-center p-4">
      <div class="container mx-auto max-w-lg">
        <!-- 主要信息卡片 -->
        <Card
          class="bg-card/80 backdrop-blur-xl border-white/30 shadow-2xl hover:shadow-3xl hover:bg-card/85 transition-all duration-500 relative overflow-hidden"
        >
          <!-- 装饰性背景 -->
          <div
            class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-pink-500/5"
          ></div>
          <div
            class="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"
          ></div>

          <CardContent class="relative p-10">
            <div class="flex flex-col items-center text-center space-y-10">
              <!-- Logo 和应用名称 -->
              <div class="flex flex-col items-center space-y-6">
                <!-- Logo -->
                <div class="relative group">
                  <!-- 外层光晕 -->
                  <div
                    class="absolute -inset-6 bg-gradient-to-r from-blue-600/30 via-purple-600/30 to-pink-600/30 rounded-full blur-2xl opacity-75 group-hover:opacity-100 transition-opacity duration-500"
                  ></div>
                  <!-- 中层光晕 -->
                  <div
                    class="absolute -inset-3 bg-gradient-to-r from-blue-500/40 to-purple-500/40 rounded-full blur-xl"
                  ></div>
                  <!-- Logo容器 -->
                  <div
                    class="relative w-24 h-24 rounded-3xl bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 p-4 shadow-2xl transform group-hover:scale-105 transition-transform duration-300"
                  >
                    <img
                      :src="LOGO"
                      alt="MattVerse Logo"
                      class="w-full h-full object-contain filter brightness-0 invert drop-shadow-sm"
                      @error="handleImageError"
                    />
                  </div>
                  <!-- 装饰性粒子 -->
                  <div
                    class="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full animate-pulse"
                  ></div>
                  <div
                    class="absolute -bottom-2 -left-2 w-2 h-2 bg-gradient-to-r from-green-400 to-blue-500 rounded-full animate-pulse delay-300"
                  ></div>
                </div>

                <!-- 应用名称 -->
                <div class="space-y-2">
                  <h2
                    class="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent tracking-tight"
                  >
                    MattVerse
                  </h2>
                  <p class="text-sm text-muted-foreground/80 font-medium tracking-wide">
                    智能化材料科学平台
                  </p>
                </div>
              </div>

              <!-- 应用信息 -->
              <div class="w-full max-w-sm space-y-1">
                <!-- 应用版本 -->
                <div
                  class="group flex justify-between items-center py-4 px-4 rounded-xl bg-gradient-to-r from-blue-50/50 to-purple-50/50 hover:from-blue-50/80 hover:to-purple-50/80 transition-all duration-300 border border-blue-100/50"
                >
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center"
                    >
                      <MattIcon name="Package" class="h-4 w-4 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">应用版本</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <Badge
                      variant="secondary"
                      class="bg-blue-100 text-blue-700 hover:bg-blue-200 font-mono text-xs px-3 py-1"
                    >
                      v{{ appVersion }}
                    </Badge>
                  </div>
                </div>

                <!-- 用户标识码 -->
                <div
                  class="group flex justify-between items-center py-4 px-4 rounded-xl bg-gradient-to-r from-purple-50/50 to-pink-50/50 hover:from-purple-50/80 hover:to-pink-50/80 transition-all duration-300 border border-purple-100/50"
                >
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-8 h-8 rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center"
                    >
                      <MattIcon name="User" class="h-4 w-4 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">用户标识码</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <span
                      class="text-sm font-mono font-semibold text-purple-700 bg-purple-100 px-3 py-1 rounded-lg"
                      >{{ userIdentifier }}</span
                    >
                  </div>
                </div>

                <!-- License有效期 -->
                <div
                  class="group flex justify-between items-center py-4 px-4 rounded-xl bg-gradient-to-r from-green-50/50 to-emerald-50/50 hover:from-green-50/80 hover:to-emerald-50/80 transition-all duration-300 border border-green-100/50"
                >
                  <div class="flex items-center space-x-3">
                    <div
                      class="w-8 h-8 rounded-lg bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center"
                    >
                      <MattIcon name="Shield" class="h-4 w-4 text-white" />
                    </div>
                    <span class="text-sm font-medium text-gray-700">License有效期</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <MattIcon name="CheckCircle" class="h-4 w-4 text-green-600" />
                    <span
                      class="text-sm font-semibold text-green-700 bg-green-100 px-3 py-1 rounded-lg"
                      >{{ licenseStatus }}</span
                    >
                  </div>
                </div>
              </div>

              <!-- 链接按钮 -->
              <div class="flex justify-center space-x-3 pt-4">
                <Button
                  variant="ghost"
                  size="sm"
                  @click="openLink('github')"
                  class="group flex items-center space-x-2 px-4 py-3 rounded-xl bg-gradient-to-r from-gray-50/50 to-gray-100/50 hover:from-gray-100/80 hover:to-gray-200/80 border border-gray-200/50 hover:border-gray-300/50 transition-all duration-300 hover:scale-105 hover:shadow-lg"
                >
                  <div
                    class="w-6 h-6 rounded-lg bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center group-hover:from-gray-800 group-hover:to-gray-900 transition-colors duration-300"
                  >
                    <MattIcon name="Github" class="h-3.5 w-3.5 text-white" />
                  </div>
                  <span class="text-sm font-medium text-gray-700 group-hover:text-gray-800"
                    >GitHub</span
                  >
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="openLink('docs')"
                  class="group flex items-center space-x-2 px-4 py-3 rounded-xl bg-gradient-to-r from-blue-50/50 to-indigo-50/50 hover:from-blue-100/80 hover:to-indigo-100/80 border border-blue-200/50 hover:border-blue-300/50 transition-all duration-300 hover:scale-105 hover:shadow-lg"
                >
                  <div
                    class="w-6 h-6 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center group-hover:from-blue-600 group-hover:to-indigo-700 transition-colors duration-300"
                  >
                    <MattIcon name="BookOpen" class="h-3.5 w-3.5 text-white" />
                  </div>
                  <span class="text-sm font-medium text-blue-700 group-hover:text-blue-800"
                    >文档</span
                  >
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  @click="openLink('website')"
                  class="group flex items-center space-x-2 px-4 py-3 rounded-xl bg-gradient-to-r from-emerald-50/50 to-green-50/50 hover:from-emerald-100/80 hover:to-green-100/80 border border-emerald-200/50 hover:border-emerald-300/50 transition-all duration-300 hover:scale-105 hover:shadow-lg"
                >
                  <div
                    class="w-6 h-6 rounded-lg bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center group-hover:from-emerald-600 group-hover:to-green-700 transition-colors duration-300"
                  >
                    <MattIcon name="Globe" class="h-3.5 w-3.5 text-white" />
                  </div>
                  <span class="text-sm font-medium text-emerald-700 group-hover:text-emerald-800"
                    >官网</span
                  >
                </Button>
              </div>

              <!-- 版权信息 -->
              <div class="pt-8">
                <div class="relative">
                  <div class="absolute inset-0 flex items-center">
                    <div
                      class="w-full border-t border-gradient-to-r from-transparent via-border/50 to-transparent"
                    ></div>
                  </div>
                  <div class="relative flex justify-center">
                    <div class="bg-card px-4">
                      <div class="flex items-center space-x-2">
                        <div
                          class="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"
                        ></div>
                        <p class="text-xs text-muted-foreground/70 font-medium tracking-wide">
                          Copyright © 2025 MattVerse. All rights reserved.
                        </p>
                        <div
                          class="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-purple-500 to-pink-500"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { logger } from '@mattverse/shared'
import type { ElectronAPI } from '@mattverse/shared'
import LOGO from '@mattverse/shared/assets/images/icons/mattverse/mattverse.png'

// 应用信息
const appVersion = ref('1.0.2')
const userIdentifier = ref('0')
const licenseStatus = ref('永久有效')

// 获取应用配置
onMounted(async () => {
  try {
    // 获取应用配置
    const electronAPI = (window as any).electronAPI as ElectronAPI | undefined

    if (electronAPI?.getConfig) {
      const config = await electronAPI.getConfig()
      appVersion.value = config.version || '1.0.2'
      logger.info('App config loaded:', config)
    }
  } catch (error) {
    logger.error('Failed to get app config:', error)
  }
})

// 处理图片加载错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 如果图片加载失败，可以设置一个默认图标或隐藏
  img.style.display = 'none'
}

// 打开链接
const openLink = (type: string) => {
  const links = {
    github: 'https://github.com/mattverse',
    docs: 'https://docs.mattverse.com',
    website: 'https://mattverse.com',
  }

  const url = links[type as keyof typeof links]
  if (url) {
    window.open(url, '_blank')
  }
}
</script>
