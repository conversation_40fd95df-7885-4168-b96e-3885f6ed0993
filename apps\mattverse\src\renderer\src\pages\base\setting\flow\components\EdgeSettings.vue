<template>
  <Card
    class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
  >
    <CardHeader>
      <CardTitle class="flex items-center space-x-2">
        <MattIcon name="GitBranch" class="h-5 w-5" />
        <span>{{ $t('settings.flow.edge.title') }}</span>
      </CardTitle>
      <CardDescription>
        {{ $t('settings.flow.edge.description') }}
      </CardDescription>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- 边缘类型 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.edge.type') }}</Label>
        <Select
          :model-value="flowSettings.edgeConfig.type"
          @update:model-value="updateEdgeConfig('type', $event)"
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="type in edgeTypes"
              :key="type.value"
              :value="type.value"
            >
              {{ type.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Separator />

      <!-- 动画效果 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.flow.edge.animated') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.flow.edge.animated_desc') }}
          </p>
        </div>
        <Switch
          :model-value="flowSettings.edgeConfig.animated"
          @update:model-value="updateEdgeConfig('animated', $event)"
        />
      </div>

      <Separator />

      <!-- 显示箭头 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.flow.edge.show_arrow') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.flow.edge.show_arrow_desc') }}
          </p>
        </div>
        <Switch
          :model-value="flowSettings.edgeConfig.showArrow"
          @update:model-value="updateEdgeConfig('showArrow', $event)"
        />
      </div>

      <Separator />

      <!-- 线条样式 -->
      <div class="space-y-4">
        <Label class="text-sm font-medium">{{ $t('settings.flow.edge.style_config') }}</Label>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 线条宽度 -->
          <div class="space-y-2">
            <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.edge.stroke_width') }}</Label>
            <div class="space-y-2">
              <Slider
                :model-value="[flowSettings.edgeConfig.style.strokeWidth]"
                :max="10"
                :min="1"
                :step="0.5"
                @update:model-value="updateEdgeStyle('strokeWidth', $event[0])"
              />
              <div class="flex justify-between text-xs text-muted-foreground">
                <span>1px</span>
                <span class="font-medium">{{ flowSettings.edgeConfig.style.strokeWidth }}px</span>
                <span>10px</span>
              </div>
            </div>
          </div>

          <!-- 线条颜色 -->
          <div class="space-y-2">
            <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.edge.stroke_color') }}</Label>
            <Input
              :model-value="flowSettings.edgeConfig.style.stroke"
              type="color"
              @update:model-value="updateEdgeStyle('stroke', $event)"
            />
          </div>
        </div>
      </div>

      <Separator />

      <!-- 箭头配置 -->
      <div class="space-y-4">
        <Label class="text-sm font-medium">{{ $t('settings.flow.edge.marker_config') }}</Label>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- 箭头类型 -->
          <div class="space-y-2">
            <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.edge.marker_type') }}</Label>
            <Select
              :model-value="flowSettings.edgeConfig.markerEnd.type"
              @update:model-value="updateEdgeMarkerEnd('type', $event)"
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="type in markerTypes"
                  :key="type.value"
                  :value="type.value"
                >
                  {{ type.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- 箭头颜色 -->
          <div class="space-y-2">
            <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.edge.marker_color') }}</Label>
            <Input
              :model-value="flowSettings.edgeConfig.markerEnd.color"
              type="color"
              @update:model-value="updateEdgeMarkerEnd('color', $event)"
            />
          </div>

          <!-- 箭头宽度 -->
          <div class="space-y-2">
            <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.edge.marker_width') }}</Label>
            <div class="space-y-2">
              <Slider
                :model-value="[flowSettings.edgeConfig.markerEnd.width]"
                :max="50"
                :min="10"
                :step="5"
                @update:model-value="updateEdgeMarkerEnd('width', $event[0])"
              />
              <div class="flex justify-between text-xs text-muted-foreground">
                <span>10px</span>
                <span class="font-medium">{{ flowSettings.edgeConfig.markerEnd.width }}px</span>
                <span>50px</span>
              </div>
            </div>
          </div>

          <!-- 箭头高度 -->
          <div class="space-y-2">
            <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.edge.marker_height') }}</Label>
            <div class="space-y-2">
              <Slider
                :model-value="[flowSettings.edgeConfig.markerEnd.height]"
                :max="50"
                :min="10"
                :step="5"
                @update:model-value="updateEdgeMarkerEnd('height', $event[0])"
              />
              <div class="flex justify-between text-xs text-muted-foreground">
                <span>10px</span>
                <span class="font-medium">{{ flowSettings.edgeConfig.markerEnd.height }}px</span>
                <span>50px</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from '@mattverse/i18n'
import type { EdgeConfig, FlowSettingsState } from '@/store'

interface Props {
  flowSettings: FlowSettingsState
  updateEdgeConfig: (key: keyof EdgeConfig, value: any) => void
  updateEdgeStyle: (key: keyof EdgeConfig['style'], value: any) => void
  updateEdgeMarkerEnd: (key: keyof EdgeConfig['markerEnd'], value: any) => void
}

defineProps<Props>()

const { t } = useI18n()

const edgeTypes = computed(() => [
  { value: 'default', label: t('settings.flow.edge.types.default') },
  { value: 'straight', label: t('settings.flow.edge.types.straight') },
  { value: 'step', label: t('settings.flow.edge.types.step') },
  { value: 'smoothstep', label: t('settings.flow.edge.types.smoothstep') },
  { value: 'bezier', label: t('settings.flow.edge.types.bezier') },
])

const markerTypes = computed(() => [
  { value: 'arrow', label: t('settings.flow.edge.marker_types.arrow') },
  { value: 'arrowclosed', label: t('settings.flow.edge.marker_types.arrowclosed') },
  { value: 'dot', label: t('settings.flow.edge.marker_types.dot') },
])
</script>
