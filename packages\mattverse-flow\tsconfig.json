{"extends": "../configs/src/typescript/vue.json", "compilerOptions": {"baseUrl": ".", "outDir": "./dist", "rootDir": "./src", "paths": {"@/*": ["./src/*"], "@components/*": ["./src/components/*"], "@stores/*": ["./src/stores/*"], "@composables/*": ["./src/composables/*"], "@utils/*": ["./src/utils/*"], "@types/*": ["./src/types/*"], "@nodes/*": ["./src/nodes/*"], "@core/*": ["./src/core/*"], "@mattverse/shared": ["../shared/src"]}}, "include": ["src/**/*.ts", "src/**/*.vue"], "exclude": ["node_modules", "dist", "**/*.test.*", "**/*.spec.*"]}