/**
 * 边相关类型定义
 */

/**
 * 边类型
 */
export type EdgeType = 'default' | 'straight' | 'step' | 'smoothstep' | 'bezier' | 'custom'

/**
 * 边数据
 */
export interface EdgeData {
  label?: string
  description?: string
  condition?: any
  config?: Record<string, any>
  [key: string]: any
}

/**
 * 工作流边
 */
export interface WorkflowEdge {
  id: string
  source: string
  target: string
  sourceHandle?: string | null
  targetHandle?: string | null
  type?: EdgeType
  data?: EdgeData
  selected?: boolean
  animated?: boolean
  hidden?: boolean
  deletable?: boolean
  selectable?: boolean
  focusable?: boolean
  style?: Record<string, any>
  class?: string
  markerStart?: string
  markerEnd?: string
  zIndex?: number
  ariaLabel?: string
  interactionWidth?: number
  createdAt?: Date
  updatedAt?: Date
}
