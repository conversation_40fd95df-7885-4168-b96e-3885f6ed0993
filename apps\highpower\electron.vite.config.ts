import { defineConfig } from 'electron-vite'
import { resolve } from 'path'
import vue from '@vitejs/plugin-vue'
import tailwindcss from '@tailwindcss/vite'
import { autoImportPresets, createCopyProtosPlugin } from '@mattverse/configs'

// 环境变量
const isDev = process.env.NODE_ENV === 'development'

export default defineConfig({
  main: {
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/main'),
        '@core': resolve(__dirname, '../../packages/electron-core/src'),
      },
    },
    plugins: [
      createCopyProtosPlugin({
        sourceDir: '../../packages/electron-core/src/grpc/protos',
        targetDir: 'out/main/protos',
        files: 'all',
        verbose: true,
      }),
    ],
    build: {
      rollupOptions: {
        external: ['@grpc/grpc-js', '@grpc/proto-loader'],
      },
    },
  },
  preload: {
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/preload'),
        '@core': resolve(__dirname, '../../packages/electron-core/src'),
      },
    },
  },
  renderer: {
    plugins: [
      vue({
        // Vue 插件优化
        script: {
          defineModel: true, // 启用 defineModel 宏
          propsDestructure: true, // 启用 props 解构
        },
      }),
      tailwindcss(),
      ...autoImportPresets.default('highpower'),
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/renderer/src'),
        '@ui': resolve(__dirname, '../../packages/mattverse-ui/src'),
        '@flow': resolve(__dirname, '../../packages/mattverse-flow/src'),
        '@configs': resolve(__dirname, '../../packages/configs/src'),
        '@shared': resolve(__dirname, '../../packages/shared/src'),
      },
    },
    publicDir: resolve(__dirname, '../../packages/shared/src/assets'),
    assetsInclude: ['**/*.ttf', '**/*.woff', '**/*.woff2'],
    optimizeDeps: {
      include: [
        // 核心依赖
        'vue',
        'vue-router',
        '@vueuse/core',
        // UI 组件库
        'lucide-vue-next',
        'radix-vue',
        // Workspace 包
        '@mattverse/shared',
        '@mattverse/mattverse-ui',
        '@mattverse/mattverse-flow',
        '@mattverse/i18n',
        '@mattverse/electron-core',
      ],
      exclude: [
        // 排除不需要预构建的包
        'electron',
        '@electron/remote',
      ],
      // 只在开发环境强制重新构建
      force: isDev,
    },
    build: {
      // 构建优化
      target: 'esnext',
      minify: 'esbuild',
      sourcemap: isDev,
      // 增加 chunk 大小警告限制到 1MB
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          // 代码分割优化
          manualChunks: {
            // Vue 生态系统
            'vue-vendor': ['vue', 'vue-router'],
            // UI 组件库
            'ui-vendor': ['radix-vue', 'lucide-vue-next'],
            // 工具库
            'utils-vendor': ['@vueuse/core', '@msgpack/msgpack'],
            // Mattverse 工作区包
            'mattverse-vendor': [
              '@mattverse/shared',
              '@mattverse/mattverse-ui',
              '@mattverse/mattverse-flow',
              '@mattverse/i18n',
              '@mattverse/electron-core',
            ],
            // 大型可视化库 - 单独分割
            'plotly-vendor': ['plotly.js'],
            'three-vendor': ['three'],
          },
          // 优化文件名
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: assetInfo => {
            const fileName = assetInfo.names?.[0] || assetInfo.name || 'unknown'
            if (/\.(css)$/.test(fileName)) {
              return 'css/[name]-[hash].[ext]'
            }
            if (/\.(png|jpe?g|gif|svg|webp|ico)$/.test(fileName)) {
              return 'images/[name]-[hash].[ext]'
            }
            if (/\.(woff2?|eot|ttf|otf)$/.test(fileName)) {
              return 'fonts/[name]-[hash].[ext]'
            }
            return 'assets/[name]-[hash].[ext]'
          },
        },
      },
    },
  },
})
