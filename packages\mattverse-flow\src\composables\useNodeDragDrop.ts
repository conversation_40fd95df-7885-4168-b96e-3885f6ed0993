import { useDebounceFn, useThrottleFn } from '@vueuse/core'
import { shallowRef } from 'vue'
import { useNodeTheme } from './useNodeTheme'

/**
 * 节点拖拽管理 Hook
 * 用于处理节点的拖拽、放置等交互
 */
export function useNodeDragDrop() {
  const { getNodeBackgroundColor } = useNodeTheme()

  // 拖拽状态
  const isDragging = shallowRef(false)

  /**
   * 处理拖拽开始
   */
  const handleDragStart = () => {
    try {
      isDragging.value = true
      document.body.classList.add('node-dragging')

      // 添加性能优化标记
      document.documentElement.style.setProperty('--edge-transition', 'none')

      // 减少重绘和回流
      requestAnimationFrame(() => {
        // 临时禁用不必要的动画和效果
        document.body.classList.add('workflow-optimized')
      })
    } catch (error) {
      console.error('拖拽开始错误:', error)
    }
  }

  /**
   * 处理拖拽中
   * 使用节流函数优化性能
   */
  const handleDrag = useThrottleFn(() => {}, 8) // 约60fps

  /**
   * 处理拖拽结束
   */
  const handleDragStop = useDebounceFn(() => {
    try {
      isDragging.value = false
      document.body.classList.remove('node-dragging')

      // 恢复动画和效果
      document.documentElement.style.removeProperty('--edge-transition')

      requestAnimationFrame(() => {
        document.body.classList.remove('workflow-optimized')
      })
    } catch (error) {
      console.error('拖拽结束错误:', error)
    }
  }, 50)

  /**
   * 处理节点放置
   * @param event 拖放事件
   * @param project 坐标转换函数
   * @param workflowId 工作流ID
   * @returns 新创建的节点数据
   */
  const handleDrop = (event, project, workflowId) => {
    event.preventDefault()

    try {
      // 解析拖放的节点数据
      const nodeData = JSON.parse(event.dataTransfer.getData('application/vueflow'))

      // 获取画布的 DOM 元素
      const reactFlowBounds = event.target.getBoundingClientRect()

      // 计算相对位置
      const position = project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      })

      // 计算节点背景色
      const nodeType = nodeData.data.nodeType || nodeData.data.type
      const nodeCategory = nodeData.data.category || ''
      const backgroundColor = getNodeBackgroundColor(nodeData.id, nodeType, nodeCategory)

      // 返回节点数据和位置
      return {
        nodeData,
        position,
        backgroundColor,
      }
    } catch (error) {
      console.error('拖放处理错误:', error)
      return null
    }
  }

  return {
    isDragging,
    handleDragStart,
    handleDrag,
    handleDragStop,
    handleDrop,
  }
}
