<template>
  <div class="flex flex-col h-full">
    <!-- 标题部分 -->
    <div class="title flex items-center">
      <span>
        <MattIcon name="Workflow" class="h-6 w-6" />
      </span>
      <span class="text-lg font-semibold ml-2 truncate block w-full">{{ workflowTitle }}</span>
    </div>
    <!-- 搜索框 -->
    <div class="search mt-4">
      <div class="relative w-full max-w-sm items-center">
        <Input id="search" v-model="searchText" type="text" placeholder="搜索..." class="pl-10" />
        <span class="absolute start-0 inset-y-0 flex items-center justify-center px-2">
          <MattIcon name="Search" class="h-6 w-6" />
        </span>
      </div>
    </div>
    <!-- 列表部分 -->
    <div class="flow-list mt-4 flex-1 overflow-x-hidden overflow-y-auto scrollbar">
      <div class="flow-list-content">
        <!-- 添加空状态判断 -->
        <template v-if="Object.keys(filteredWorkflowNodeList).length === 0">
          <MattEmptyState
            icon="Waypoints"
            :icon-size="48"
            title="暂无可用工具"
            description="当前没有可用的工作流工具，请在工具管理中启用相关功能"
          >
            <template #action>
              <Button variant="outline" size="sm" as-child>
                <router-link to="/tools/node">
                  <MattIcon name="Settings" class="mr-2 h-4 w-4" />
                  前往工具管理
                </router-link>
              </Button>
            </template>
          </MattEmptyState>
        </template>

        <!-- 遍历大标题 -->
        <template
          v-for="(section, sectionName) in filteredWorkflowNodeList"
          v-else
          :key="sectionName"
        >
          <!-- 大标题 -->
          <div class="flow-list-item flex flex-col select-none">
            <div
              class="flex items-center justify-between text-lg font-semibold"
              @click="toggleSection(sectionName)"
            >
              <div class="flex items-center">
                <span class="flex items-center truncate">
                  <MattIcon
                    v-if="section.icon.type === 'icon'"
                    :name="section.icon.value"
                    class="h-6 w-6 mr-2"
                  />

                  <img
                    v-else
                    :src="section.icon.value"
                    :width="24"
                    :height="24"
                    class="object-contain mr-2"
                  />
                  {{ sectionName }}
                </span>
              </div>
              <span
                class="transition-transform duration-300"
                :class="{ 'rotate-180': !collapsedSections[sectionName] }"
              >
                <MattIcon
                  :name="collapsedSections[sectionName] ? 'Plus' : 'Minus'"
                  class="h-6 w-6"
                />
              </span>
            </div>

            <!-- 遍历分类 -->
            <Transition
              name="expand"
              @enter="expandEnter"
              @after-enter="expandAfterEnter"
              @leave="expandLeave"
            >
              <div v-show="!collapsedSections[sectionName]">
                <div v-for="category in section.categories" :key="category.name" class="mt-4">
                  <!-- 分类标题 -->
                  <div class="flex items-center justify-between px-2 text-sm">
                    <span class="truncate w-full">{{ category.name }}</span>
                  </div>

                  <!-- 节点网格 -->
                  <div class="p-2 text-sm">
                    <div class="grid grid-cols-2 gap-2">
                      <div v-for="node in category.nodes" :key="node.id" class="mb-2">
                        <div
                          class="bg-[#f2f2f2] rounded-lg w-full h-16 flex flex-col justify-evenly p-2 group cursor-move hover:bg-[#e5e5e5]"
                          :style="{ backgroundColor: getNodeBackgroundColor(node) || '#f2f2f2' }"
                          draggable="true"
                          @dragstart="onDragStart($event, node)"
                        >
                          <div class="flex items-center justify-between relative">
                            <MattIcon
                              v-if="node.data.icon.type === 'icon'"
                              :name="node.data.icon.value"
                              class="h-6 w-6"
                            />
                            <img
                              v-else
                              :src="node.data.icon.value"
                              :width="24"
                              :height="24"
                              class="object-contain"
                            />
                            <div
                              class="tips flex items-center justify-center gap-1 absolute -top-1 right-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                            >
                              <MattIcon
                                v-tooltip="node.data.description"
                                name="CircleAlert"
                                class="h-4 w-4 cursor-help"
                                @mouseenter="showTooltip(node)"
                              />
                            </div>
                          </div>
                          <div v-tooltip="node.data.label" class="">
                            <span class="font-serif font-sm truncate block w-full">
                              {{ node.data.label }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Transition>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, onBeforeUnmount, onMounted, ref, watch, getCurrentInstance } from 'vue'
import { themeColorMap, VTooltip as vTooltip } from '@mattverse/shared'
import { MattIcon, MattEmptyState } from '@mattverse/mattverse-ui'
import { useNodeModulesStore, useToolsStore } from '@/stores'

const app = getCurrentInstance()
if (app) {
  app.appContext.app.directive('tooltip', vTooltip)
}

const props = defineProps({
  workflowTitle: {
    type: String,
    required: true,
  },
  theme: {
    type: String,
    default: 'city-light',
  },
})

const toolsStore = useToolsStore()
const nodeModulesStore = useNodeModulesStore()
// 当前主题 - 使用设置存储
const currentTheme = computed(() => props.theme || 'city-light')
// const workflowNodeList = WORKFLOW_NODE_OBJ
const searchText = ref('')
const collapsedSections = ref({}) // 存储每个section的折叠状态
const expandedCategories = ref({})
// 过滤后的节点列表
const filteredWorkflowNodeList = computed(() => {
  const searchQuery = searchText.value.toLowerCase().trim()
  const toolsStatus = toolsStore.toolsStatus

  // 获取已启用的节点模块
  const enabledModules = nodeModulesStore.enabledNodeModules
  // 根据工具状态过滤
  const filteredByTools = Object.entries(enabledModules).reduce((acc, [sectionName, section]) => {
    // 检查该部分是否启用
    if (toolsStatus[section.type]) {
      acc[sectionName] = section
    }
    return acc
  }, {})

  if (!searchQuery) {
    return filteredByTools
  }

  const filteredList = {}

  // 遍历所有部分
  Object.entries(filteredByTools).forEach(([sectionName, section]) => {
    const filteredCategories = section.categories
      .map(category => {
        // 过滤节点
        const filteredNodes = category.nodes.filter(
          node =>
            node.data.label.toLowerCase().includes(searchQuery) ||
            node.data.type.toLowerCase().includes(searchQuery) ||
            node.data.description.toLowerCase().includes(searchQuery)
        )

        // 如果该类别有匹配的节点，返回过滤后的类别
        if (filteredNodes.length > 0) {
          return {
            ...category,
            nodes: filteredNodes,
          }
        }
        return null
      })
      .filter(Boolean) // 移除空类别

    // 如果该部分有匹配的类别，添加到过滤后的列表中
    if (filteredCategories.length > 0) {
      filteredList[sectionName] = {
        ...section,
        categories: filteredCategories,
      }
    }
  })
  return filteredList
})
// 切换section的折叠状态
const toggleSection = sectionName => {
  // console.log('toggleSection', sectionName)
  collapsedSections.value[sectionName] = !collapsedSections.value[sectionName]
}
// 处理拖拽开始
const onDragStart = (event, node) => {
  // console.log('Node onDragStart:', event, node)
  const nodeData = {
    ...node,
    position: { x: 0, y: 0 }, // 位置将在放置时更新
  }
  event.dataTransfer.setData('application/vueflow', JSON.stringify(nodeData))
  event.dataTransfer.effectAllowed = 'move'
}

// 显示节点提示
const showTooltip = node => {
  // console.log('Node description:', node.data.description)
}
// 展开动画处理函数
const expandEnter = element => {
  const width = getComputedStyle(element).width
  element.style.width = width
  element.style.position = 'absolute'
  element.style.visibility = 'hidden'
  element.style.height = 'auto'
  const height = getComputedStyle(element).height
  element.style.width = null
  element.style.position = null
  element.style.visibility = null
  element.style.height = 0
  // 触发重绘
  getComputedStyle(element).height
  element.style.height = height
}

const expandAfterEnter = element => {
  element.style.height = 'auto'
}

const expandLeave = element => {
  const height = getComputedStyle(element).height
  element.style.height = height
  // 触发重绘
  getComputedStyle(element).height
  element.style.height = 0
}

// 根据节点类型和当前主题获取背景色
const getNodeBackgroundColor = node => {
  // 如果节点自定义了背景色，优先使用
  if (node.data.backgroundColor) {
    return node.data.backgroundColor
  }

  const nodeType = node.data.nodeType || node.data.type
  const nodeCategory = node.data.category || ''

  // 根据节点类型和类别确定颜色键前缀
  let colorKeyPrefix = ''

  // 材料设计节点
  if (nodeCategory === 'materialDesign') {
    if (nodeType === 'Basic') {
      colorKeyPrefix = 'material-basic'
    } else if (nodeType === 'Compute') {
      colorKeyPrefix = 'material-compute'
    } else if (nodeType === 'Data') {
      colorKeyPrefix = 'material-data'
    } else {
      // 默认为材料基础组件
      colorKeyPrefix = 'material-basic'
    }
  }
  // 电池模拟节点
  else if (nodeCategory === 'batterySimulation') {
    if (nodeType === 'Basic') {
      colorKeyPrefix = 'battery-basic'
    } else if (nodeType === 'Compute') {
      colorKeyPrefix = 'battery-compute'
    } else if (nodeType === 'Data') {
      colorKeyPrefix = 'battery-data'
    } else {
      // 默认为电池基础组件
      colorKeyPrefix = 'battery-basic'
    }
  }
  // 默认颜色
  else {
    colorKeyPrefix = 'material-basic'
  }

  // 获取当前主题下该类型的颜色数组
  const themeColors = themeColorMap[currentTheme.value] || {}
  const colorArray = themeColors[colorKeyPrefix] || []

  if (colorArray.length === 0) {
    return '#f2f2f2' // 默认颜色
  }

  // 计算节点索引，用于循环显示颜色
  // 使用节点ID的哈希值来确保相同ID的节点总是获得相同的颜色
  const hashCode = str => {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      hash = (hash << 5) - hash + str.charCodeAt(i)
      hash |= 0 // 转换为32位整数
    }
    return Math.abs(hash)
  }

  // 根据节点ID计算使用哪个颜色
  const colorIndex = hashCode(node.id) % colorArray.length
  return colorArray[colorIndex] || '#f2f2f2'
}

// 主题变化现在通过 Pinia 设置存储自动响应

// 监听工具状态变化
watch(
  () => toolsStore.toolsStatus,
  () => {
    console.log('工具状态已更新，节点列表已重新过滤')
  },
  { deep: true }
)

// 组件挂载时添加事件监听
onMounted(() => {
  // 初始化折叠状态 - 默认展开所有节点
  const modules = nodeModulesStore.enabledNodeModules
  console.log(modules)
  Object.keys(modules).forEach(moduleName => {
    collapsedSections.value[moduleName] = false
  })
  // console.log(filteredWorkflowNodeList, '-----------loading-----------')
  // 确保工具状态已同步
  Object.values(nodeModulesStore.nodeModules).forEach(module => {
    if (module.enabled) {
      toolsStore.addTool(module.type, true)
    }
  })
})

// 组件卸载时移除事件监听
onBeforeUnmount(() => {
  // 清理逻辑
})
</script>

<style lang="scss" scoped>
// 展开/折叠动画
.expand-enter-active,
.expand-leave-active {
  transition: height 0.3s ease;
  overflow: hidden;
}

.expand-enter-from,
.expand-leave-to {
  height: 0;
}

// 图标旋转动画
.rotate-180 {
  transform: rotate(180deg);
}
</style>
