/**
 * 工作流序列化器
 */
import type { Workflow } from '../types'

export class WorkflowSerializer {
  /**
   * 序列化工作流
   */
  serialize(workflow: Workflow): string {
    try {
      // 创建序列化对象，处理日期等特殊类型
      const serializable = {
        ...workflow,
        createdAt: workflow.createdAt.toISOString(),
        updatedAt: workflow.updatedAt.toISOString(),
        publishedAt: workflow.publishedAt?.toISOString(),
        nodes: workflow.nodes.map(node => ({
          ...node,
          createdAt: node.createdAt?.toISOString(),
          updatedAt: node.updatedAt?.toISOString(),
        })),
        edges: workflow.edges.map(edge => ({
          ...edge,
          createdAt: edge.createdAt?.toISOString(),
          updatedAt: edge.updatedAt?.toISOString(),
        })),
      }

      return JSON.stringify(serializable, null, 2)
    } catch (error) {
      throw new Error(
        `Failed to serialize workflow: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * 反序列化工作流
   */
  deserialize(data: string): Workflow {
    try {
      const parsed = JSON.parse(data)

      // 恢复日期对象
      const workflow: Workflow = {
        ...parsed,
        createdAt: new Date(parsed.createdAt),
        updatedAt: new Date(parsed.updatedAt),
        publishedAt: parsed.publishedAt ? new Date(parsed.publishedAt) : undefined,
        nodes: parsed.nodes.map((node: any) => ({
          ...node,
          createdAt: node.createdAt ? new Date(node.createdAt) : new Date(),
          updatedAt: node.updatedAt ? new Date(node.updatedAt) : new Date(),
        })),
        edges: parsed.edges.map((edge: any) => ({
          ...edge,
          createdAt: edge.createdAt ? new Date(edge.createdAt) : new Date(),
          updatedAt: edge.updatedAt ? new Date(edge.updatedAt) : new Date(),
        })),
      }

      return workflow
    } catch (error) {
      throw new Error(
        `Failed to deserialize workflow: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * 导出为 JSON 文件格式
   */
  exportToFile(workflow: Workflow): Blob {
    const data = this.serialize(workflow)
    return new Blob([data], { type: 'application/json' })
  }

  /**
   * 从文件导入
   */
  async importFromFile(file: File): Promise<Workflow> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()

      reader.onload = event => {
        try {
          const data = event.target?.result as string
          const workflow = this.deserialize(data)
          resolve(workflow)
        } catch (error) {
          reject(error)
        }
      }

      reader.onerror = () => {
        reject(new Error('Failed to read file'))
      }

      reader.readAsText(file)
    })
  }
}
