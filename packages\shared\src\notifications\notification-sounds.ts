/**
 * 通知音效管理
 * 使用 Web Audio API 生成和播放通知音效
 */

export type NotificationSoundType = 'success' | 'error' | 'warning' | 'info' | 'default'

export class NotificationSounds {
  private audioContext: AudioContext | null = null
  private isEnabled = true

  constructor() {
    this.initAudioContext()
  }

  /**
   * 初始化音频上下文
   */
  private initAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    } catch (error) {
      console.warn('Web Audio API not supported:', error)
      this.audioContext = null
    }
  }

  /**
   * 设置音效开关
   */
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled
  }

  /**
   * 播放通知音效
   */
  async play(type: NotificationSoundType = 'default') {
    if (!this.isEnabled || !this.audioContext) {
      return
    }

    try {
      // 如果音频上下文被暂停，尝试恢复
      if (this.audioContext.state === 'suspended') {
        await this.audioContext.resume()
      }

      switch (type) {
        case 'success':
          this.playSuccessSound()
          break
        case 'error':
          this.playErrorSound()
          break
        case 'warning':
          this.playWarningSound()
          break
        case 'info':
          this.playInfoSound()
          break
        default:
          this.playDefaultSound()
          break
      }
    } catch (error) {
      console.warn('Failed to play notification sound:', error)
    }
  }

  /**
   * 播放成功音效 - 上升的双音调
   */
  private playSuccessSound() {
    if (!this.audioContext) return

    const oscillator1 = this.audioContext.createOscillator()
    const oscillator2 = this.audioContext.createOscillator()
    const gainNode = this.audioContext.createGain()

    oscillator1.connect(gainNode)
    oscillator2.connect(gainNode)
    gainNode.connect(this.audioContext.destination)

    oscillator1.frequency.setValueAtTime(523.25, this.audioContext.currentTime) // C5
    oscillator2.frequency.setValueAtTime(659.25, this.audioContext.currentTime) // E5

    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
    gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.01)
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.3)

    oscillator1.start(this.audioContext.currentTime)
    oscillator2.start(this.audioContext.currentTime)
    oscillator1.stop(this.audioContext.currentTime + 0.3)
    oscillator2.stop(this.audioContext.currentTime + 0.3)
  }

  /**
   * 播放错误音效 - 下降的低音调
   */
  private playErrorSound() {
    if (!this.audioContext) return

    const oscillator = this.audioContext.createOscillator()
    const gainNode = this.audioContext.createGain()

    oscillator.connect(gainNode)
    gainNode.connect(this.audioContext.destination)

    oscillator.frequency.setValueAtTime(220, this.audioContext.currentTime) // A3
    oscillator.frequency.exponentialRampToValueAtTime(110, this.audioContext.currentTime + 0.4) // A2

    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
    gainNode.gain.linearRampToValueAtTime(0.15, this.audioContext.currentTime + 0.01)
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.4)

    oscillator.start(this.audioContext.currentTime)
    oscillator.stop(this.audioContext.currentTime + 0.4)
  }

  /**
   * 播放警告音效 - 重复的中音调
   */
  private playWarningSound() {
    if (!this.audioContext) return

    const playBeep = (startTime: number) => {
      const oscillator = this.audioContext!.createOscillator()
      const gainNode = this.audioContext!.createGain()

      oscillator.connect(gainNode)
      gainNode.connect(this.audioContext!.destination)

      oscillator.frequency.setValueAtTime(440, startTime) // A4

      gainNode.gain.setValueAtTime(0, startTime)
      gainNode.gain.linearRampToValueAtTime(0.1, startTime + 0.01)
      gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + 0.15)

      oscillator.start(startTime)
      oscillator.stop(startTime + 0.15)
    }

    const currentTime = this.audioContext.currentTime
    playBeep(currentTime)
    playBeep(currentTime + 0.2)
  }

  /**
   * 播放信息音效 - 单一清脆音调
   */
  private playInfoSound() {
    if (!this.audioContext) return

    const oscillator = this.audioContext.createOscillator()
    const gainNode = this.audioContext.createGain()

    oscillator.connect(gainNode)
    gainNode.connect(this.audioContext.destination)

    oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime) // 高音调

    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
    gainNode.gain.linearRampToValueAtTime(0.08, this.audioContext.currentTime + 0.01)
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.2)

    oscillator.start(this.audioContext.currentTime)
    oscillator.stop(this.audioContext.currentTime + 0.2)
  }

  /**
   * 播放默认音效 - 温和的提示音
   */
  private playDefaultSound() {
    if (!this.audioContext) return

    const oscillator = this.audioContext.createOscillator()
    const gainNode = this.audioContext.createGain()

    oscillator.connect(gainNode)
    gainNode.connect(this.audioContext.destination)

    oscillator.frequency.setValueAtTime(600, this.audioContext.currentTime)

    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime)
    gainNode.gain.linearRampToValueAtTime(0.06, this.audioContext.currentTime + 0.01)
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.25)

    oscillator.start(this.audioContext.currentTime)
    oscillator.stop(this.audioContext.currentTime + 0.25)
  }

  /**
   * 销毁音频上下文
   */
  destroy() {
    if (this.audioContext) {
      this.audioContext.close()
      this.audioContext = null
    }
  }
}

// 创建全局实例
export const notificationSounds = new NotificationSounds()
