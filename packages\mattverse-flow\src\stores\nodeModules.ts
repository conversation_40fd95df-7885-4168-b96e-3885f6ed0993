import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { nodeModules, type NodeModule } from '@mattverse/shared'
import { createPersistConfig } from '@/stores/plugins/persist-config'

import { decode } from '@msgpack/msgpack'
import { nanoid } from 'nanoid'

// 解析 msgpack 数据
const tryDecodeMsgPack = async (
  file: File
): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const buffer = await file.arrayBuffer()
    try {
      const decodedData: unknown = decode(new Uint8Array(buffer))
      if (
        typeof decodedData === 'object' &&
        decodedData !== null &&
        'data' in decodedData &&
        typeof (decodedData as any).data === 'string'
      ) {
        try {
          const jsonString = decodeURIComponent(escape(atob((decodedData as any).data)))
          const jsonData = JSON.parse(jsonString)
          return { success: true, data: jsonData }
        } catch {
          // Base64 解码或 JSON 解析失败，继续尝试其他方法
        }
      }
      if (typeof decodedData === 'object' && decodedData !== null) {
        return { success: true, data: decodedData }
      }
    } catch {
      // msgpack 解析失败，尝试其他方法
    }
    try {
      const text = new TextDecoder().decode(buffer)
      if (text.trim().startsWith('{') && text.trim().endsWith('}')) {
        const jsonData = JSON.parse(text)
        return { success: true, data: jsonData }
      }
    } catch {
      // JSON 解析失败
    }
    return {
      success: false,
      error: '无法解析文件格式，请确保文件格式正确',
    }
  } catch (error) {
    return {
      success: false,
      error: `解析失败: ${error instanceof Error ? error.message : '未知错误'}`,
    }
  }
}

// 解析 JSON 数据
const tryParseJSON = async (
  file: File
): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const content = await file.text()
    const data = JSON.parse(content)
    return { success: true, data }
  } catch {
    return {
      success: false,
      error: '文件格式错误：请确保文件是有效的JSON格式',
    }
  }
}

export const useNodeModulesStore = defineStore(
  'nodeModules',
  () => {
    // 存储所有节点模块
    const nodeModulesData = ref<Record<string, NodeModule>>({})

    // 初始化数据 - 确保总是有数据
    const initializeModules = () => {
      // 总是使用默认数据作为基础
      const defaultData = { ...nodeModules }

      // 如果没有数据或数据为空，直接使用默认数据
      if (!nodeModulesData.value || Object.keys(nodeModulesData.value).length === 0) {
        nodeModulesData.value = defaultData
        return
      }

      // 如果有持久化数据，合并默认数据和持久化数据
      const mergedData = { ...nodeModulesData.value }

      // 更新内置模块，保留持久化的启用状态，但使用最新的模块定义
      Object.keys(defaultData).forEach(key => {
        if (mergedData[key]) {
          // 深度合并模块数据，保留持久化的 enabled 状态和用户自定义的节点数据
          const existingModule = mergedData[key]
          const defaultModule = defaultData[key]

          // 合并分类数据，保留用户自定义的节点
          const mergedCategories =
            defaultModule.categories?.map(defaultCat => {
              const existingCat = existingModule.categories?.find(
                cat => cat.name === defaultCat.name
              )
              if (existingCat) {
                // 合并节点数据，优先使用用户数据，但确保默认节点的完整性
                const mergedNodes =
                  defaultCat.nodes?.map(defaultNode => {
                    const existingNode = existingCat.nodes?.find(node => node.id === defaultNode.id)
                    if (existingNode) {
                      // 保留用户修改，但确保默认字段的完整性
                      return {
                        ...defaultNode,
                        ...existingNode,
                        data: {
                          ...defaultNode.data,
                          ...existingNode.data,
                        },
                      }
                    }
                    return defaultNode
                  }) || []

                // 添加用户自定义的节点
                const userCustomNodes =
                  existingCat.nodes?.filter(
                    node => !defaultCat.nodes?.some(defaultNode => defaultNode.id === node.id)
                  ) || []

                return {
                  ...defaultCat,
                  ...existingCat,
                  nodes: [...mergedNodes, ...userCustomNodes],
                }
              }
              return defaultCat
            }) || []

          // 添加用户自定义的分类
          const userCustomCategories =
            existingModule.categories?.filter(
              cat => !defaultModule.categories?.some(defaultCat => defaultCat.name === cat.name)
            ) || []

          mergedData[key] = {
            ...defaultModule,
            ...existingModule,
            enabled: existingModule.enabled ?? defaultModule.enabled,
            categories: [...mergedCategories, ...userCustomCategories],
          }
        } else {
          // 新的内置模块，直接添加
          mergedData[key] = defaultData[key]
        }
      })

      nodeModulesData.value = mergedData
    }

    // 立即初始化
    initializeModules()

    // 获取所有模块
    const getAllModules = computed(() => {
      return nodeModulesData.value
    })

    // 获取启用的模块
    const getEnabledModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => module.enabled)
      )
    })

    // 获取禁用的模块
    const getDisabledModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => !module.enabled)
      )
    })

    // 获取内置模块
    const getBuiltinModules = computed(() => {
      return Object.fromEntries(
        Object.entries(nodeModulesData.value).filter(([_, module]) => module.isBuiltin)
      )
    })

    // 切换模块启用状态
    const toggleModuleEnabled = (moduleName: string) => {
      if (nodeModulesData.value[moduleName]) {
        nodeModulesData.value[moduleName].enabled = !nodeModulesData.value[moduleName].enabled
      }
    }

    // 设置模块启用状态
    const setModuleEnabled = (moduleName: string, enabled: boolean) => {
      if (nodeModulesData.value[moduleName]) {
        nodeModulesData.value[moduleName].enabled = enabled
      }
    }

    // 获取单个模块
    const getModule = (moduleName: string) => {
      return nodeModulesData.value[moduleName] || null
    }

    // 检查模块是否启用
    const isModuleEnabled = (moduleName: string) => {
      return nodeModulesData.value[moduleName]?.enabled || false
    }

    // 添加新模块
    const addModule = (moduleName: string, moduleData: NodeModule) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { enabled, isBuiltin, ...cleanModuleData } = moduleData
      nodeModulesData.value[moduleName] = {
        ...cleanModuleData,
        enabled: true,
        isBuiltin: false,
      }
    }

    // 删除模块
    const deleteModule = (moduleName: string) => {
      if (nodeModulesData.value[moduleName] && !nodeModulesData.value[moduleName].isBuiltin) {
        delete nodeModulesData.value[moduleName]
        return true
      }
      return false
    }

    // 更新模块（支持重命名）
    const updateModule = (oldName: string, updates: Partial<NodeModule> & { name?: string }) => {
      const existing = nodeModulesData.value[oldName]
      if (!existing) return false

      const nextName = updates.name && updates.name.trim().length > 0 ? updates.name : oldName

      const normalizedIcon = (() => {
        const incoming = updates.icon as any
        if (!incoming) return existing.icon as any
        if (typeof incoming === 'string') {
          return { type: 'icon', value: incoming }
        }
        if (typeof incoming === 'object' && incoming.type && incoming.value) {
          return { type: incoming.type, value: incoming.value }
        }
        return existing.icon as any
      })()

      const merged: NodeModule = {
        ...existing,
        ...updates,
        icon: normalizedIcon,
      }

      // 若重命名为新 key
      if (nextName !== oldName) {
        // 避免覆盖已有同名模块
        if (nodeModulesData.value[nextName] && nextName !== oldName) {
          return false
        }
        merged.name = nextName
        nodeModulesData.value[nextName] = merged
        delete nodeModulesData.value[oldName]
      } else {
        nodeModulesData.value[oldName] = merged
      }
      return true
    }

    // 分类 CRUD
    const addCategory = (moduleName: string, category: { name: string; sort?: number }) => {
      const mod = nodeModulesData.value[moduleName]
      if (!mod) return false
      if (!mod.categories) mod.categories = []
      if (mod.categories.find(c => c.name === category.name)) return false
      mod.categories.push({ name: category.name, sort: category.sort, nodes: [] })
      return true
    }

    const updateCategory = (
      moduleName: string,
      categoryName: string,
      updates: { name?: string; sort?: number }
    ) => {
      const mod = nodeModulesData.value[moduleName]
      if (!mod || !mod.categories) return false
      const cat = mod.categories.find(c => c.name === categoryName)
      if (!cat) return false
      if (updates.name && updates.name !== categoryName) {
        if (mod.categories.find(c => c.name === updates.name)) return false
        cat.name = updates.name
      }
      if (typeof updates.sort === 'number') cat.sort = updates.sort
      return true
    }

    const deleteCategory = (moduleName: string, categoryName: string) => {
      const mod = nodeModulesData.value[moduleName]
      if (!mod || !mod.categories) return false
      const idx = mod.categories.findIndex(c => c.name === categoryName)
      if (idx === -1) return false
      mod.categories.splice(idx, 1)
      return true
    }

    // 节点 CRUD
    const addNode = (
      moduleName: string,
      categoryName: string,
      node: {
        id?: string
        type: string
        data: NodeModule['categories'][number]['nodes'][number]['data']
      }
    ) => {
      const mod = nodeModulesData.value[moduleName]
      if (!mod || !mod.categories) return false
      const cat = mod.categories.find(c => c.name === categoryName)
      if (!cat) return false
      const id = node.id && node.id.length > 0 ? node.id : nanoid()
      const normalizedIcon = (() => {
        const incoming = node.data.icon as any
        if (!incoming) return { type: 'icon', value: 'Package' }
        if (typeof incoming === 'object' && incoming.type && incoming.value) return incoming
        if (typeof incoming === 'string') return { type: 'icon', value: incoming }
        return { type: 'icon', value: 'Package' }
      })()
      cat.nodes.push({ id, type: node.type, data: { ...node.data, icon: normalizedIcon } })
      return true
    }

    const updateNode = (
      moduleName: string,
      nodeId: string,
      updates: Partial<{
        type: string
        data: NodeModule['categories'][number]['nodes'][number]['data']
      }> & { moveToCategory?: string }
    ) => {
      const mod = nodeModulesData.value[moduleName]
      if (!mod || !mod.categories) return false
      let fromCatIdx = -1
      let nodeIdx = -1
      for (let i = 0; i < mod.categories.length; i++) {
        const idx = mod.categories[i].nodes.findIndex(n => n.id === nodeId)
        if (idx !== -1) {
          fromCatIdx = i
          nodeIdx = idx
          break
        }
      }
      if (fromCatIdx === -1 || nodeIdx === -1) return false

      const sourceCat = mod.categories[fromCatIdx]
      const targetNode = sourceCat.nodes[nodeIdx]

      // 处理图标归一化
      let nextData = targetNode.data
      if (updates.data) {
        const inc = updates.data as any
        const nextIcon = (() => {
          const incoming = inc.icon as any
          if (!incoming) return nextData.icon
          if (typeof incoming === 'object' && incoming.type && incoming.value) return incoming
          if (typeof incoming === 'string') return { type: 'icon', value: incoming }
          return nextData.icon
        })()
        nextData = { ...nextData, ...inc, icon: nextIcon }
      }

      const updated = {
        ...targetNode,
        ...(updates.type ? { type: updates.type } : {}),
        data: nextData,
      }

      // 是否需要移动分类
      if (updates.moveToCategory && updates.moveToCategory !== mod.categories[fromCatIdx].name) {
        const toCat = mod.categories.find(c => c.name === updates.moveToCategory)
        if (!toCat) return false
        // 从原分类移除
        sourceCat.nodes.splice(nodeIdx, 1)
        // 添加到目标分类
        toCat.nodes.push(updated)
      } else {
        sourceCat.nodes.splice(nodeIdx, 1, updated)
      }
      return true
    }

    const deleteNode = (moduleName: string, nodeId: string) => {
      const mod = nodeModulesData.value[moduleName]
      if (!mod || !mod.categories) return false
      for (const cat of mod.categories) {
        const idx = cat.nodes.findIndex(n => n.id === nodeId)
        if (idx !== -1) {
          cat.nodes.splice(idx, 1)
          return true
        }
      }
      return false
    }

    // 导入新的节点模块
    const importNodeModule = async (file: File): Promise<{ success: boolean; message: string }> => {
      try {
        const isMsgPack = file.name.endsWith('.plugin')
        let parseResult: { success: boolean; data?: any; error?: string }

        if (isMsgPack) {
          parseResult = await tryDecodeMsgPack(file)
        } else {
          parseResult = await tryParseJSON(file)
        }

        if (!parseResult.success) {
          return {
            success: false,
            message: parseResult.error || '文件解析失败',
          }
        }

        let moduleData = parseResult.data

        // 处理单个模块数据结构
        const keys = Object.keys(moduleData)
        if (keys.length === 1 && typeof moduleData[keys[0]] === 'object' && !moduleData.name) {
          const moduleName = keys[0]
          moduleData = {
            name: moduleName,
            ...moduleData[moduleName],
          }
        }

        // 验证必要字段
        if (!moduleData.name) {
          return { success: false, message: '缺少必要字段：name' }
        }

        if (!moduleData.type) {
          return { success: false, message: '缺少必要字段：type' }
        }

        if (!Array.isArray(moduleData.categories)) {
          return { success: false, message: '缺少必要字段：categories 或格式不正确' }
        }

        // 处理节点数据，为没有ID的节点生成ID
        moduleData.categories.forEach((category: any) => {
          if (Array.isArray(category.nodes)) {
            category.nodes.forEach((node: any) => {
              if (!node.id) {
                node.id = nanoid()
              }
            })
          }
        })

        // 添加模块到存储中，确保导入的模块默认启用
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { enabled, isBuiltin, ...cleanModuleData } = moduleData
        nodeModulesData.value[moduleData.name] = {
          ...cleanModuleData,
          enabled: true,
          isBuiltin: false,
        }

        return { success: true, message: `成功导入节点模块: ${moduleData.name}` }
      } catch (error) {
        return {
          success: false,
          message: `导入失败: ${error instanceof Error ? error.message : '未知错误'}`,
        }
      }
    }

    // 获取模块列表
    const getModuleList = computed(() => {
      return Object.entries(nodeModulesData.value)
        .map(([key, module]) => ({
          key,
          name: module.name,
          description: module.description,
          icon: module.icon,
          enabled: module.enabled,
          isBuiltin: module.isBuiltin,
          type: module.type,
          sort: module.sort || 999,
          categoriesCount: module.categories?.length || 0,
          nodesCount:
            module.categories?.reduce(
              (total, category) => total + (category.nodes?.length || 0),
              0
            ) || 0,
        }))
        .sort((a, b) => a.sort - b.sort)
    })

    // 获取排序后的模块分类
    const getSortedCategories = (moduleName: string) => {
      const module = nodeModulesData.value[moduleName]
      if (!module || !module.categories) return []

      return [...module.categories].sort((a, b) => (a.sort || 999) - (b.sort || 999))
    }

    // 获取排序后的分类节点
    const getSortedNodes = (moduleName: string, categoryName: string) => {
      const module = nodeModulesData.value[moduleName]
      if (!module || !module.categories) return []

      const category = module.categories.find(cat => cat.name === categoryName)
      if (!category || !category.nodes) return []

      return [...category.nodes].sort((a, b) => (a.data?.sort || 999) - (b.data?.sort || 999))
    }

    return {
      // 状态
      nodeModulesData,

      // 计算属性
      getAllModules,
      getEnabledModules,
      getDisabledModules,
      getBuiltinModules,
      getModuleList,

      // 方法
      initializeModules,
      toggleModuleEnabled,
      setModuleEnabled,
      getModule,
      isModuleEnabled,
      addModule,
      deleteModule,
      updateModule,
      addCategory,
      updateCategory,
      deleteCategory,
      addNode,
      updateNode,
      deleteNode,
      importNodeModule,
      getSortedCategories,
      getSortedNodes,
    }
  },
  {
    persist: createPersistConfig('nodeModules'),
  }
)
