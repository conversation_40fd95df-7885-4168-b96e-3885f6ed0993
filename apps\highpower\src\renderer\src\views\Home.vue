<template>
  <div class="home-page">
    <div class="container mx-auto px-4 py-8">
      <div class="text-center mb-8">
        <h1 class="text-4xl font-bold text-gray-800 mb-4">欢迎使用 HighPower</h1>
        <p class="text-lg text-gray-600">高性能计算工作流平台</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- 工作流编辑器卡片 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-xl font-semibold">工作流编辑器</h3>
          </div>
          <div class="card-body">
            <p class="text-gray-600 mb-4">创建和编辑复杂的计算工作流，支持可视化拖拽操作。</p>
            <router-link to="/workflow" class="btn btn-primary"> 打开编辑器 </router-link>
          </div>
        </div>

        <!-- gRPC 测试卡片 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-xl font-semibold">gRPC 测试</h3>
          </div>
          <div class="card-body">
            <p class="text-gray-600 mb-4">测试与后端服务的 gRPC 连接和通信功能。</p>
            <router-link to="/grpc-test" class="btn btn-secondary"> 开始测试 </router-link>
          </div>
        </div>

        <!-- 系统信息卡片 -->
        <div class="card">
          <div class="card-header">
            <h3 class="text-xl font-semibold">系统信息</h3>
          </div>
          <div class="card-body">
            <div class="space-y-2 text-sm">
              <p><span class="font-medium">应用版本:</span> {{ appInfo.version || '加载中...' }}</p>
              <p><span class="font-medium">平台:</span> {{ systemInfo.platform || '加载中...' }}</p>
              <p>
                <span class="font-medium">Node.js:</span>
                {{ systemInfo.nodeVersion || '加载中...' }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- 功能特性 -->
      <div class="mt-12">
        <h2 class="text-2xl font-bold text-center mb-8">核心功能</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div class="feature-item">
            <h3 class="text-lg font-semibold mb-2">可视化工作流设计</h3>
            <p class="text-gray-600">
              通过直观的拖拽界面设计复杂的计算工作流，支持多种节点类型和连接方式。
            </p>
          </div>
          <div class="feature-item">
            <h3 class="text-lg font-semibold mb-2">高性能计算支持</h3>
            <p class="text-gray-600">集成 GPU 计算能力，支持大规模并行计算任务的调度和执行。</p>
          </div>
          <div class="feature-item">
            <h3 class="text-lg font-semibold mb-2">实时状态监控</h3>
            <p class="text-gray-600">实时监控工作流执行状态，提供详细的日志和性能指标。</p>
          </div>
          <div class="feature-item">
            <h3 class="text-lg font-semibold mb-2">分布式架构</h3>
            <p class="text-gray-600">基于 gRPC 的分布式架构，支持多节点协同计算和负载均衡。</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 响应式数据
const appInfo = ref<any>({})
const systemInfo = ref<any>({})

// 获取系统信息
const loadSystemInfo = async () => {
  try {
    if (window.electronAPI) {
      appInfo.value = await window.electronAPI.getAppInfo()
      systemInfo.value = await window.electronAPI.getSystemInfo()
    }
  } catch (error) {
    console.error('Failed to load system info:', error)
  }
}

onMounted(() => {
  loadSystemInfo()
})
</script>

<style scoped>
.home-page {
  min-height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 0;
}

.container {
  max-width: 1200px;
}

.card {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition:
    transform 0.2s,
    box-shadow 0.2s;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 1.5rem 1.5rem 0;
}

.card-body {
  padding: 1rem 1.5rem 1.5rem;
}

.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background: #4b5563;
}

.feature-item {
  padding: 1.5rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}
</style>
