import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { nanoid } from 'nanoid'
import { useDebounceFn } from '@vueuse/core'
import { createPersistConfig } from '@/stores/plugins/persist-config'

/**
 * 工作流项目接口
 */
export interface WorkflowItem {
  id: string
  title: string
  description: string
  createTime: string
  updateTime: string
  folderId: string
  tags?: string[]
  isTemplate?: boolean
}

/**
 * 工作流创建参数
 */
export interface CreateWorkflowParams {
  title: string
  description?: string
  folderId?: string
  tags?: string[]
  isTemplate?: boolean
}

/**
 * 工作流更新参数
 */
export interface UpdateWorkflowParams {
  id: string
  title?: string
  description?: string
  folderId?: string
  tags?: string[]
  isTemplate?: boolean
}

/**
 * 工作流状态管理
 */
export const useWorkflowStore = defineStore(
  'workflow',
  () => {
    // 状态
    const workflows = ref<WorkflowItem[]>([])
    const currentWorkflowId = ref<string>('')
    const searchKeyword = ref<string>('')

    // 计算属性
    const currentWorkflow = computed(() =>
      workflows.value.find(wf => wf.id === currentWorkflowId.value)
    )

    const filteredWorkflows = computed(() => {
      if (!searchKeyword.value) return workflows.value

      const keyword = searchKeyword.value.toLowerCase()
      return workflows.value.filter(
        wf =>
          wf.title.toLowerCase().includes(keyword) ||
          wf.description.toLowerCase().includes(keyword) ||
          wf.tags?.some(tag => tag.toLowerCase().includes(keyword))
      )
    })

    // 根据文件夹ID获取工作流
    const getWorkflowsByFolder = (folderId: string) => {
      if (folderId === 'all') return workflows.value
      return workflows.value.filter(wf => wf.folderId === folderId)
    }

    // 获取当前文件夹的工作流（结合搜索）
    const getCurrentFolderWorkflows = (folderId: string) => {
      const folderWorkflows = getWorkflowsByFolder(folderId)
      if (!searchKeyword.value) return folderWorkflows

      const keyword = searchKeyword.value.toLowerCase()
      return folderWorkflows.filter(
        wf =>
          wf.title.toLowerCase().includes(keyword) ||
          wf.description.toLowerCase().includes(keyword) ||
          wf.tags?.some(tag => tag.toLowerCase().includes(keyword))
      )
    }

    // 操作方法
    const createWorkflow = (params: CreateWorkflowParams): WorkflowItem => {
      // 检查同名工作流并生成唯一名称
      let title = params.title
      let counter = 1

      while (
        workflows.value.some(wf => wf.title === title && wf.folderId === (params.folderId || 'all'))
      ) {
        title = `${params.title} (${counter})`
        counter++
      }

      const now = new Date().toLocaleString()
      const newWorkflow: WorkflowItem = {
        id: nanoid(),
        title,
        description: params.description || '',
        createTime: now,
        updateTime: now,
        folderId: params.folderId || 'all',
        tags: params.tags || [],
        isTemplate: params.isTemplate || false,
      }

      workflows.value.push(newWorkflow)
      return newWorkflow
    }

    const updateWorkflow = (params: UpdateWorkflowParams): boolean => {
      const index = workflows.value.findIndex(wf => wf.id === params.id)
      if (index === -1) return false

      const workflow = workflows.value[index]
      const updatedWorkflow = {
        ...workflow,
        ...params,
        updateTime: new Date().toLocaleString(),
      }

      workflows.value[index] = updatedWorkflow
      return true
    }

    const deleteWorkflow = (workflowId: string): boolean => {
      const index = workflows.value.findIndex(wf => wf.id === workflowId)
      if (index === -1) return false

      workflows.value.splice(index, 1)

      // 如果删除的是当前工作流，清空当前工作流ID
      if (currentWorkflowId.value === workflowId) {
        currentWorkflowId.value = ''
      }

      return true
    }

    const duplicateWorkflow = (workflowId: string): WorkflowItem | null => {
      const workflow = workflows.value.find(wf => wf.id === workflowId)
      if (!workflow) return null

      return createWorkflow({
        title: `${workflow.title} - 副本`,
        description: workflow.description,
        folderId: workflow.folderId,
        tags: [...(workflow.tags || [])],
        isTemplate: workflow.isTemplate,
      })
    }

    // 批量删除指定文件夹下的工作流
    const deleteWorkflowsByFolder = (folderId: string): number => {
      const initialCount = workflows.value.length
      workflows.value = workflows.value.filter(wf => wf.folderId !== folderId)

      // 如果当前工作流被删除，清空当前工作流ID
      if (currentWorkflow.value && currentWorkflow.value.folderId === folderId) {
        currentWorkflowId.value = ''
      }

      return initialCount - workflows.value.length
    }

    // 移动工作流到指定文件夹
    const moveWorkflowToFolder = (workflowId: string, targetFolderId: string): boolean => {
      return updateWorkflow({ id: workflowId, folderId: targetFolderId })
    }

    // 设置当前工作流
    const setCurrentWorkflow = (workflowId: string) => {
      currentWorkflowId.value = workflowId
    }

    // 设置搜索关键词
    const setSearchKeyword = useDebounceFn((keyword: string) => {
      searchKeyword.value = keyword
    }, 300)

    // 立即设置搜索关键词
    const setSearchKeywordImmediate = (keyword: string) => {
      searchKeyword.value = keyword
    }

    // 清空搜索
    const clearSearch = () => {
      searchKeyword.value = ''
    }

    return {
      // 状态
      workflows,
      currentWorkflowId,
      searchKeyword,

      // 计算属性
      currentWorkflow,
      filteredWorkflows,

      // 方法
      getWorkflowsByFolder,
      getCurrentFolderWorkflows,
      createWorkflow,
      updateWorkflow,
      deleteWorkflow,
      duplicateWorkflow,
      deleteWorkflowsByFolder,
      moveWorkflowToFolder,
      setCurrentWorkflow,
      setSearchKeyword,
      setSearchKeywordImmediate,
      clearSearch,
    }
  },
  {
    persist: createPersistConfig('workflow'),
  }
)
