<template>
  <AlertDialog :open="isOpen" @update:open="handleOpenChange">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>删除连线</AlertDialogTitle>
        <AlertDialogDescription>确定要删除这条连线吗？此操作无法撤销。</AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel @click="handleCancel">取消</AlertDialogCancel>
        <AlertDialogAction class="bg-red-500 hover:bg-red-400" @click="handleConfirm">
          确认删除
        </AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>

<script setup lang="ts">
const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  edgeId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update:isOpen', 'confirm', 'cancel'])

const handleOpenChange = (open: boolean) => {
  emit('update:isOpen', open)
}

const handleConfirm = () => {
  emit('confirm', props.edgeId)
  emit('update:isOpen', false)
}

const handleCancel = () => {
  emit('cancel')
  emit('update:isOpen', false)
}
</script>
