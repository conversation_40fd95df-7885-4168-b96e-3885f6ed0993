import { useFlowsStore, useNodeNavbarStore } from '@/stores'
import { useDebounceFn, useThrottleFn } from '@vueuse/core'
import { nanoid } from 'nanoid'
import { computed, shallowRef, watch, onUnmounted } from 'vue'

import type { WorkflowNode, WorkflowEdge } from '@/types'
/**
 * 工作流元素管理 Hook
 * 用于管理工作流中的节点和边
 * @param workflowId 工作流ID
 */
export function useWorkflowElements(workflowId: string) {
  const flowsStore = useFlowsStore()
  const nodeNavbarStore = useNodeNavbarStore()

  // 定义节点和边类型
  // interface FlowNode {
  //   id: string
  //   position: { x: number; y: number }
  //   data: any
  //   [key: string]: any
  // }

  // interface FlowEdge {
  //   id: string
  //   source: string
  //   target: string
  //   [key: string]: any
  // }

  // 单独存储节点和边，提高性能
  const nodes = shallowRef<WorkflowNode[]>([])
  const edges = shallowRef<WorkflowEdge[]>([])

  // 从 store 同步数据到本地
  const syncFromStore = () => {
    const workflow = flowsStore.getWorkflow(workflowId)
    if (workflow) {
      nodes.value = workflow.nodes || []
      edges.value = workflow.edges || []
    }
  }

  // 注册同步回调，当外部更新节点数据时自动同步
  flowsStore.registerSyncCallback(workflowId, syncFromStore)

  // 组件卸载时注销回调
  onUnmounted(() => {
    flowsStore.unregisterSyncCallback(workflowId, syncFromStore)
  })

  // 历史记录用于撤销/重做
  const history = shallowRef<any[]>([])
  const currentHistoryIndex = shallowRef(-1)

  // 计算是否可以撤销/重做
  const canUndo = computed(() => currentHistoryIndex.value > 0)
  const canRedo = computed(() => currentHistoryIndex.value < history.value.length - 1)

  // 加载工作流数据
  const loadWorkflow = () => {
    const savedWorkflow = flowsStore.getWorkflow(workflowId)
    if (savedWorkflow && savedWorkflow.nodes) {
      // 分别存储节点和边
      nodes.value = savedWorkflow.nodes || []
      edges.value = savedWorkflow.edges || []
      return true
    }
    nodes.value = []
    edges.value = []
    return false
  }

  // 立即保存工作流
  const saveWorkflowImmediate = () => {
    const workflowData = { nodes: nodes.value, edges: edges.value }
    flowsStore.saveWorkflow(workflowId, workflowData)
  }

  const saveWorkflow = useDebounceFn(saveWorkflowImmediate, 100)

  // 保存当前状态到历史记录
  const saveState = useDebounceFn(state => {
    // 移除当前索引之后的历史记录
    history.value = history.value.slice(0, currentHistoryIndex.value + 1)
    history.value.push(state)
    currentHistoryIndex.value++
  }, 500)

  // 撤销
  const undo = () => {
    if (currentHistoryIndex.value > 0) {
      currentHistoryIndex.value--
      const previousState = history.value[currentHistoryIndex.value]
      nodes.value = previousState.nodes
      edges.value = previousState.edges
      saveWorkflow()
    }
  }

  // 重做
  const redo = () => {
    if (currentHistoryIndex.value < history.value.length - 1) {
      currentHistoryIndex.value++
      const nextState = history.value[currentHistoryIndex.value]
      nodes.value = nextState.nodes
      edges.value = nextState.edges
      saveWorkflow()
    }
  }

  // 添加节点
  const addNode = (nodeData, position) => {
    // 生成新的唯一ID
    const newId = nanoid()

    // 创建新节点
    const newNode = {
      ...nodeData,
      id: newId,
      position,
      type: nodeData.type || 'custom',
      data: {
        ...nodeData.data,
        workflowId,
        params: nodeData.data.params || {},
      },
    }

    // 添加到节点数组
    nodes.value = [...nodes.value, newNode]

    // 保存状态
    const currentState = {
      nodes: nodes.value,
      edges: edges.value,
    }
    saveState(currentState)
    saveWorkflow()

    return newNode
  }

  // 更新节点
  const updateNode = (nodeId, updates) => {
    // 查找节点索引
    const nodeIndex = nodes.value.findIndex(node => node.id === nodeId)

    if (nodeIndex !== -1) {
      const originalNode = nodes.value[nodeIndex]

      // 更新节点，确保位置信息正确合并
      const updatedNodes = [...nodes.value]
      updatedNodes[nodeIndex] = {
        ...originalNode,
        ...updates,
        // 如果更新中没有位置信息，保持原有位置
        position: updates.position || originalNode.position,
      }
      nodes.value = updatedNodes

      // 保存状态
      const currentState = {
        nodes: nodes.value,
        edges: edges.value,
      }
      saveState(currentState)
      saveWorkflow()
    } else {
      // 可能是边，查找边索引
      const edgeIndex = edges.value.findIndex(edge => edge.id === nodeId)

      if (edgeIndex !== -1) {
        // 更新边
        const updatedEdges = [...edges.value]
        updatedEdges[edgeIndex] = { ...updatedEdges[edgeIndex], ...updates }
        edges.value = updatedEdges

        // 保存状态
        const currentState = {
          nodes: nodes.value,
          edges: edges.value,
        }
        saveState(currentState)
        saveWorkflow()
      }
    }
  }

  // 删除节点
  const deleteNode = useThrottleFn(nodeId => {
    // 删除与该节点相关的边
    edges.value = edges.value.filter(edge => !(edge.source === nodeId || edge.target === nodeId))

    // 删除节点本身
    nodes.value = nodes.value.filter(node => node.id !== nodeId)

    // 删除节点参数
    flowsStore.deleteNodeParams(workflowId, nodeId)

    // 保存状态
    const currentState = {
      nodes: nodes.value,
      edges: edges.value,
    }
    saveState(currentState)
    saveWorkflow()
  }, 200)

  // 批量删除节点
  const deleteMultipleNodes = useThrottleFn(nodeIds => {
    if (!nodeIds || nodeIds.length === 0) return

    // 删除对应的节点设置标签页
    nodeIds.forEach(nodeId => {
      const tabToRemove = nodeNavbarStore.tabs.find(tab => tab.nodeId === nodeId)
      if (tabToRemove) {
        nodeNavbarStore.removeTab(tabToRemove.id)
      }
    })

    // 删除与这些节点相关的边
    edges.value = edges.value.filter(
      edge => !nodeIds.includes(edge.source) && !nodeIds.includes(edge.target)
    )

    // 删除节点本身
    nodes.value = nodes.value.filter(node => !nodeIds.includes(node.id))

    // 删除节点参数
    nodeIds.forEach(nodeId => {
      flowsStore.deleteNodeParams(workflowId, nodeId)
    })

    // 保存状态
    const currentState = {
      nodes: nodes.value,
      edges: edges.value,
    }
    saveState(currentState)
    saveWorkflow()
  }, 200)

  // 添加边
  const addEdge = (params, options = {}) => {
    const edge = {
      id: `edge-${nanoid()}`,
      source: params.source,
      target: params.target,
      ...options,
    }

    // 添加到边数组
    edges.value = [...edges.value, edge]

    // 保存状态
    const currentState = {
      nodes: nodes.value,
      edges: edges.value,
    }
    saveState(currentState)
    saveWorkflow()

    return edge
  }

  // 删除边
  const deleteEdge = edgeId => {
    // 删除边
    edges.value = edges.value.filter(edge => edge.id !== edgeId)

    // 保存状态
    const currentState = {
      nodes: nodes.value,
      edges: edges.value,
    }
    saveState(currentState)
    saveWorkflow()
  }

  // 水平对齐选中的节点
  const alignHorizontal = useThrottleFn(() => {
    // 过滤出被选中的节点
    const selectedNodes = nodes.value.filter(node => node.selected)

    if (selectedNodes.length < 2) {
      return
    }

    const avgY =
      selectedNodes.reduce((sum, node) => sum + node.position.y, 0) / selectedNodes.length

    const updatedNodes = [...nodes.value]
    selectedNodes.forEach(node => {
      const index = updatedNodes.findIndex(n => n.id === node.id)
      if (index !== -1) {
        updatedNodes[index] = {
          ...updatedNodes[index],
          position: {
            ...updatedNodes[index].position,
            y: avgY,
          },
        }
      }
    })

    nodes.value = updatedNodes

    // 保存状态
    const currentState = {
      nodes: nodes.value,
      edges: edges.value,
    }
    saveState(currentState)
    saveWorkflow()
  }, 200)

  // 垂直对齐选中的节点
  const alignVertical = useThrottleFn(() => {
    // 过滤出被选中的节点
    const selectedNodes = nodes.value.filter(node => node.selected)

    if (selectedNodes.length < 2) {
      return
    }

    const avgX =
      selectedNodes.reduce((sum, node) => sum + node.position.x, 0) / selectedNodes.length

    const updatedNodes = [...nodes.value]
    selectedNodes.forEach(node => {
      const index = updatedNodes.findIndex(n => n.id === node.id)
      if (index !== -1) {
        updatedNodes[index] = {
          ...updatedNodes[index],
          position: {
            ...updatedNodes[index].position,
            x: avgX,
          },
        }
      }
    })

    nodes.value = updatedNodes

    // 保存状态
    const currentState = {
      nodes: nodes.value,
      edges: edges.value,
    }
    saveState(currentState)
    saveWorkflow()
  }, 200)

  // 检查是否有多个节点被选中
  const hasMultipleNodesSelected = computed(() => {
    const selectedNodes = nodes.value.filter(node => node.selected)
    return selectedNodes.length >= 2
  })

  // 重置边样式
  const resetEdgeStyles = (defaultEdgeStyle, selectedEdgeId = null) => {
    // 只更新边的样式
    const updatedEdges = edges.value.map(edge => {
      // 如果是被选中的边，保持其样式不变
      if (selectedEdgeId && edge.id === selectedEdgeId) {
        return edge
      }

      // 其他边重置为默认样式
      return {
        ...edge,
        animated: false,
        style: {
          ...(defaultEdgeStyle?.style || {}),
          strokeDasharray: undefined,
        },
        markerEnd: defaultEdgeStyle?.markerEnd
          ? {
              ...defaultEdgeStyle.markerEnd,
            }
          : undefined,
      }
    })

    edges.value = updatedEdges
  }

  // 监听工作流ID变化
  watch(
    () => workflowId,
    newId => {
      if (newId) {
        loadWorkflow()
      }
    },
    { immediate: true }
  )

  return {
    nodes,
    edges,
    loadWorkflow,
    saveWorkflow,
    saveWorkflowImmediate,
    addNode,
    updateNode,
    deleteNode,
    deleteMultipleNodes,
    addEdge,
    deleteEdge,
    alignHorizontal,
    alignVertical,
    hasMultipleNodesSelected,
    resetEdgeStyles,
    syncFromStore,
    undo,
    redo,
    canUndo,
    canRedo,
    history,
    currentHistoryIndex,
  }
}
