<template>
  <Card
    class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
  >
    <CardHeader>
      <CardTitle class="flex items-center space-x-2">
        <MattIcon name="Image" class="h-5 w-5" />
        <span>{{ $t('settings.flow.background.title') }}</span>
      </CardTitle>
      <CardDescription>
        {{ $t('settings.flow.background.description') }}
      </CardDescription>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- 背景图案类型 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.background.variant') }}</Label>
        <Select
          :model-value="flowSettings.backgroundConfig.variant"
          @update:model-value="updateBackgroundConfig('variant', $event)"
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="variant in backgroundVariants"
              :key="variant.value"
              :value="variant.value"
            >
              {{ variant.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Separator />

      <!-- 图案颜色 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.background.pattern_color') }}</Label>
        <Input
          :model-value="flowSettings.backgroundConfig.patternColor"
          type="color"
          @update:model-value="updateBackgroundConfig('patternColor', $event)"
        />
      </div>

      <Separator />

      <!-- 间距 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.background.gap') }}</Label>
        <div class="space-y-2">
          <Slider
            :model-value="[flowSettings.backgroundConfig.gap]"
            :max="100"
            :min="5"
            :step="5"
            @update:model-value="updateBackgroundConfig('gap', $event[0])"
          />
          <div class="flex justify-between text-xs text-muted-foreground">
            <span>5px</span>
            <span class="font-medium">{{ flowSettings.backgroundConfig.gap }}px</span>
            <span>100px</span>
          </div>
        </div>
      </div>

      <Separator />

      <!-- 大小 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.background.size') }}</Label>
        <div class="space-y-2">
          <Slider
            :model-value="[flowSettings.backgroundConfig.size]"
            :max="10"
            :min="0.5"
            :step="0.5"
            @update:model-value="updateBackgroundConfig('size', $event[0])"
          />
          <div class="flex justify-between text-xs text-muted-foreground">
            <span>0.5</span>
            <span class="font-medium">{{ flowSettings.backgroundConfig.size }}</span>
            <span>10</span>
          </div>
        </div>
      </div>

      <Separator />

      <!-- 预览 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.background.preview') }}</Label>
        <div 
          class="w-full h-32 border rounded-md relative overflow-hidden"
          :style="{
            backgroundColor: '#ffffff',
            backgroundImage: getBackgroundPattern(),
            backgroundSize: `${flowSettings.backgroundConfig.gap}px ${flowSettings.backgroundConfig.gap}px`,
          }"
        >
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="bg-white/80 backdrop-blur-sm px-3 py-1 rounded text-xs text-muted-foreground">
              {{ $t('settings.flow.background.preview_text') }}
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from '@mattverse/i18n'
import type { BackgroundConfig, FlowSettingsState } from '@/store'

interface Props {
  flowSettings: FlowSettingsState
  updateBackgroundConfig: (key: keyof BackgroundConfig, value: any) => void
}

const props = defineProps<Props>()

const { t } = useI18n()

const backgroundVariants = computed(() => [
  { value: 'dots', label: t('settings.flow.background.variants.dots') },
  { value: 'lines', label: t('settings.flow.background.variants.lines') },
  { value: 'cross', label: t('settings.flow.background.variants.cross') },
])

const getBackgroundPattern = () => {
  const { variant, patternColor, size } = props.flowSettings.backgroundConfig
  const color = patternColor
  const dotSize = size
  
  switch (variant) {
    case 'dots':
      return `radial-gradient(circle, ${color} ${dotSize}px, transparent ${dotSize}px)`
    case 'lines':
      return `linear-gradient(${color} ${dotSize}px, transparent ${dotSize}px), linear-gradient(90deg, ${color} ${dotSize}px, transparent ${dotSize}px)`
    case 'cross':
      return `linear-gradient(${color} ${dotSize}px, transparent ${dotSize}px), linear-gradient(90deg, ${color} ${dotSize}px, transparent ${dotSize}px)`
    default:
      return `radial-gradient(circle, ${color} ${dotSize}px, transparent ${dotSize}px)`
  }
}
</script>
