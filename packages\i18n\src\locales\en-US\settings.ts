// Settings related
export default {
  // Basic settings
  title: 'System Settings',
  basic_desc: 'Manage basic application configuration and preferences',
  language: 'Language Settings',
  language_desc: 'Choose the display language for the application interface',
  theme: 'Theme Settings',
  theme_desc: 'Customize the application theme and colors',
  font: 'Font Settings',
  font_desc: 'Adjust the application font style and size',
  appearance: 'Appearance Settings',
  appearance_desc: 'Customize the appearance and language of the application',
  cache: 'Cache Settings',
  cache_desc: 'Manage application cache and storage settings',
  notifications: 'Notification Settings',
  notifications_desc: 'Manage how and when the application shows notifications',
  advanced: 'Advanced Settings',
  advanced_desc: 'Configure advanced features and system behavior',
  advanced_detailed: 'Advanced Settings Details',
  advanced_detailed_desc: 'Configure backup, performance and developer options',
  backup_settings: 'Backup Settings',
  performance_settings: 'Performance Settings',
  developer_options: 'Developer Options',

  // Language settings
  language_options: {
    auto: 'Auto Detect',
    zh_cn: '简体中文',
    en_us: 'English',
  },

  // Theme settings
  theme_options: {
    light: 'Light Theme',
    dark: 'Dark Theme',
    system: 'Follow System',
    current_display: 'Current Display',
  },

  // Theme colors
  theme_colors: 'Theme Colors',
  use_custom_colors: 'Use Custom Colors',
  cancel_custom_colors: 'Cancel Custom',
  using_default_neutral_colors: 'Using default neutral colors',
  select_custom_color_or_cancel:
    'Select a custom color, or click "Cancel Custom" to return to default colors',
  color_themes: {
    'city-light': 'City Light',
    'forest-light': 'Forest Light',
    'lake-light': 'Lake Light',
    'desert-light': 'Desert Light',
    'farm-light': 'Farm Light',
    'garden-light': 'Garden Light',
    'city-dark': 'City Dark',
    'forest-dark': 'Forest Dark',
    'lake-dark': 'Lake Dark',
    'desert-dark': 'Desert Dark',
    'farm-dark': 'Farm Dark',
    'garden-dark': 'Garden Dark',
  },

  // Font settings
  font_family: 'Font Family',
  font_size: 'Font Size',
  font_category: 'Font Category',
  font_preview: 'Font Preview',
  font_preview_title: 'Font Preview Effect',
  font_preview_text: 'This is a font preview text to demonstrate the current selected font effect.',
  font_size_preview: 'Font Size Comparison',

  // Font categories
  font_categories: {
    sans: 'Sans-serif',
    serif: 'Serif',
    mono: 'Monospace',
  },

  // Font options
  font_options: {
    system: 'System Font',
    default: 'Default Font',
    noto: 'Noto Sans SC',
    noto_light: 'Noto Sans SC Light',
    noto_medium: 'Noto Sans SC Medium',
    noto_extrabold: 'Noto Sans SC ExtraBold',
    source_han_sc: 'Source Han Sans SC',
    alibaba_puhuiti: 'Alibaba PuHuiTi',
    alibaba_hk: 'Alibaba Sans HK',
    serif: 'Serif Font',
    monospace: 'Monospace Font',
  },

  // Cache settings
  cache_options: {
    title: 'Cache Settings',
    description: 'Manage application cache data, clear unnecessary cache',
    enable_cache: 'Enable Cache',
    enable_cache_desc: 'Enable caching to improve application performance',

    // Storage types
    storage_types: 'Storage Types',
    http_cache: 'HTTP Cache',
    http_cache_desc: 'Store long-term cached data, such as user preferences',
    indexeddb: 'IndexedDB Database',
    indexeddb_desc: 'Client-side database for storing large amounts of structured data',
    localstorage: 'LocalStorage',
    localstorage_desc: 'Small data stored in the browser by web pages',
    sessionstorage: 'SessionStorage',
    sessionstorage_desc: 'Store temporary session data, deleted after browser closes',
    cookies: 'Cookies',
    cookies_desc: 'Small data stored in the browser by web pages',
    appcache: 'Application Cache',
    appcache_desc: 'Caching mechanism for offline access to web applications',
    serviceworkers: 'Service Workers',
    serviceworkers_desc: 'Background scripts for caching and offline web requests',
    pinia_store: 'Pinia Store',
    pinia_store_desc: 'Application state management data',
    app_settings_cache: 'App Settings Cache',
    app_settings_cache_desc: 'Application configuration and user settings data',

    // Settings
    max_size: 'Max Size',
    entries: 'Entries',

    // Select to clear
    select_to_clear: 'Select Cache to Clear',
    app_settings: 'App Settings',
    theme_settings: 'Theme Settings',
    theme_settings_desc: 'Application theme and color configuration',
    language_settings: 'Language Settings',
    language_settings_desc: 'Interface language preferences',
    font_settings: 'Font Settings',
    font_settings_desc: 'Application font size and style',
    workflow_data: 'Workflow Data',
    workflow_data_desc: 'Workflow files and job information',
    navigation_history: 'Navigation History',
    navigation_history_desc: 'Workflow navigation and history',
    tool_settings: 'Tool Settings',
    tool_settings_desc: 'Tool state and configuration data',

    // Auto clean
    auto_clean: 'Auto Clean',
    enable_auto_clean: 'Enable Auto Clean',
    auto_clean_desc: 'Automatically clear expired cache data periodically',
    clean_interval: 'Clean Interval',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    max_age: 'Max Age',
    days: 'Days',

    // Clear on exit
    clear_on_exit: 'Clear Cache on Exit',
    clear_on_exit_desc: 'Automatically clear all cache when application exits',

    // Action buttons
    cache_actions: 'Cache Actions',
    clear_selected: 'Clear Selected',
    clear_all: 'Clear All',
    refresh_info: 'Refresh Info',

    // Cache info
    cache_info: 'Cache Information',
    last_updated: 'Last Updated',
    total_cache_size: 'Total Cache Size',

    // Messages
    selected_cache_cleared: 'Selected cache cleared',
    all_cache_cleared: 'All cache cleared',
  },

  // Log Settings
  log_options: {
    title: 'Log Settings',
    description: 'Manage application logging and files',

    // Basic Configuration
    enable_logging: 'Enable Logging',
    enable_logging_desc: 'Enable application logging when turned on',
    basic_config: 'Basic Configuration',

    // Log Levels
    log_level: 'Log Level',
    level_error: 'Error',
    level_warn: 'Warning',
    level_info: 'Info',
    level_debug: 'Debug',
    level_verbose: 'Verbose',

    // Console Display
    show_in_console: 'Show in Console',
    show_in_console_desc: 'Display logs in browser developer console',

    // File Configuration
    file_config: 'File Configuration',
    log_path: 'Log Path',
    default_path: 'Use default path',
    open_folder: 'Open Folder',
    filename_pattern: 'Filename Pattern',
    filename_pattern_desc: 'Supported variables: {process}=process name, {date}=date, {time}=time',
    max_file_size: 'Max File Size',
    max_files: 'Max Files',

    // Auto Cleanup
    auto_cleanup: 'Auto Cleanup',
    enable_auto_cleanup: 'Enable Auto Cleanup',
    auto_cleanup_desc: 'Automatically clean up expired log files',
    cleanup_days: 'Retention Days',
    days: 'days',

    // Advanced Options
    advanced_options: 'Advanced Options',
    enable_file_rotation: 'Enable File Rotation',
    file_rotation_desc: 'Automatically create new files when reaching max size',
    compress_old_logs: 'Compress Old Logs',
    compress_logs_desc: 'Automatically compress old log files to save space',

    // Log Actions
    log_actions: 'Log Actions',
    refresh_logs: 'Refresh List',
    cleanup_now: 'Cleanup Now',
    clear_all_logs: 'Clear All Logs',

    // Log File List
    log_files: 'Log Files',
    total_files: 'Total Files',
    no_log_files: 'No log files found',
    file_size: 'File Size',
    modified: 'Modified',
    open: 'Open',
    copy_path: 'Copy Path',
    cache_cleared_success: 'Cache cleared successfully',
    cache_cleared_failed: 'Failed to clear cache',
    get_cache_info_failed: 'Failed to get cache information',
  },

  // Notification settings
  notification_options: {
    enable_notifications: 'Enable Notifications',
    enable_notifications_desc: 'Allow the app to send notification messages',
    desktop_notifications: 'Desktop Notifications',
    desktop_notifications_desc: 'Show notification popups on desktop',
    sound_notifications: 'Sound Notifications',
    sound_notifications_desc: 'Play notification sounds',
  },

  // Advanced settings
  advanced_options: {
    debug_mode: 'Debug Mode',
    debug_mode_desc: 'Enable debug mode for detailed error information',
    auto_save: 'Auto Save',
    auto_save_desc: 'Automatically save work progress and setting changes',
    backup_frequency: 'Backup Frequency',
    cache_size: 'Cache Size',
    log_level: 'Log Level',
  },

  // Backup options
  backup_options: {
    never: 'Never Backup',
    daily: 'Daily Backup',
    weekly: 'Weekly Backup',
    monthly: 'Monthly Backup',
  },

  // Action messages
  export_settings: 'Export Settings',
  export_success: 'Settings exported successfully',
  export_failed: 'Failed to export settings',
  reset_to_defaults: 'Reset to Defaults',
  reset_success: 'Settings have been reset to default values',
  save_success: 'Settings saved successfully',

  // About information
  about: {
    title: 'About',
    subtitle: 'Application information and system details',
    app_info: 'Application Info',
    system_info: 'System Information',
    development_team: 'Development Team',
    open_source_license: 'Open Source License',
    related_links: 'Related Links',

    // Application info
    app_name: 'MattVerse',
    app_description:
      'A modern desktop application that provides users with excellent experience and powerful functionality.',
    version: 'Version',
    build_type: 'Build Type',
    development: 'Development',
    production: 'Production',

    // System information
    operating_system: 'Operating System',
    architecture: 'Architecture',
    nodejs_version: 'Node.js Version',
    electron_version: 'Electron Version',
    chrome_version: 'Chrome Version',

    // Development team
    chief_developer: 'Chief Developer',

    // License
    license_type: 'License Type',
    license_description:
      'This software is open source under the MIT license, allowing free use, modification and distribution.',
    view_license: 'View License',

    // Action buttons
    check_updates: 'Check Updates',
    official_website: 'Official Website',

    // Links
    github: 'GitHub',
    documentation: 'Documentation',
    support: 'Support',
    feedback: 'Feedback',

    // Copyright info
    copyright: 'Copyright',
    website: 'Official Website',
  },

  // Flow settings
  flow: {
    title: 'Flow Settings',
    description: 'Configure workflow editor and execution engine parameters',

    // Execution settings
    execution: {
      title: 'Execution Settings',
      description: 'Configure workflow execution mode and performance parameters',
      mode: 'Execution Mode',
      modes: {
        sequential: 'Sequential',
        parallel: 'Parallel',
        mixed: 'Mixed Mode',
      },
      max_concurrent: 'Max Concurrent Nodes',
      timeout: 'Execution Timeout',
    },

    // Editor settings
    editor: {
      title: 'Editor Settings',
      description: 'Configure workflow editor behavior and validation rules',
      connection_mode: 'Connection Mode',
      connection_modes: {
        auto: 'Auto Connect',
        manual: 'Manual Connect',
        smart: 'Smart Connect',
      },
      validation_level: 'Validation Level',
      validation_levels: {
        none: 'No Validation',
        basic: 'Basic Validation',
        strict: 'Strict Validation',
      },
      layout_direction: 'Layout Direction',
      layout_directions: {
        horizontal: 'Horizontal',
        vertical: 'Vertical',
        auto: 'Auto Layout',
      },
    },

    // Grid settings
    grid: {
      title: 'Grid Settings',
      description: 'Configure editor grid display and alignment features',
      enabled: 'Enable Grid',
      enabled_desc: 'Show grid lines to help align nodes',
      size: 'Grid Size',
      snap_to_grid: 'Snap to Grid',
      snap_to_grid_desc: 'Automatically align nodes to grid when dragging',
      show_grid: 'Show Grid',
      show_grid_desc: 'Display grid lines in the editor',
    },

    // Zoom settings
    zoom: {
      title: 'Zoom Settings',
      description: 'Configure editor zoom range and default values',
      min_zoom: 'Minimum Zoom',
      max_zoom: 'Maximum Zoom',
      default_zoom: 'Default Zoom',
    },

    // Interface settings
    interface: {
      title: 'Interface Settings',
      description: 'Configure editor interface element display',
      show_minimap: 'Show Minimap',
      show_minimap_desc: 'Display thumbnail in bottom-right corner of editor',
      show_controls: 'Show Controls',
      show_controls_desc: 'Display zoom and fit view controls',
      show_background: 'Show Background',
      show_background_desc: 'Display editor background pattern',
      enable_animations: 'Enable Animations',
      enable_animations_desc: 'Enable animation effects for nodes and connections',
    },
  },

  // Middleware settings
  middleware: {
    title: 'Middleware Settings',
    subtitle: 'Configure middleware server connection and related settings',
    connection_config: 'Connection Configuration',
    connection_config_desc: 'Configure middleware server connection parameters',
    connection_status: 'Connection Status',
    advanced_settings: 'Advanced Settings',
    advanced_settings_desc: 'Configure connection timeout and reconnection options',

    // Connection configuration
    host: 'Server Address',
    host_placeholder: 'Enter server address',
    host_required: 'Server address is required',
    host_invalid: 'Please enter a valid IP address or domain name',
    port: 'Port',
    port_placeholder: 'Enter port number',
    test_connection: 'Test Connection',

    // Connection status
    status: 'Connection Status',
    current_address: 'Current Service Address',
    version: 'Middleware Version',

    // Status text
    status_idle: 'Disconnected',
    status_connecting: 'Connecting',
    status_ready: 'Connected',
    status_failed: 'Connection Failed',
    status_shutdown: 'Disconnected',

    // Advanced settings
    connection_timeout: 'Connection Timeout',
    auto_reconnect: 'Auto Reconnect',
    auto_reconnect_desc: 'Automatically attempt to reconnect when connection is lost',

    // Confirmation dialog
    confirm_changes: 'Confirm Changes',
    confirm_changes_desc:
      'Changing connection configuration will require restarting the application to take effect',
    new_host: 'New Server Address',
    new_port: 'New Port',
    restart_warning:
      'After clicking confirm, the application will automatically restart to apply the new configuration.',
    restart_confirm:
      'Configuration saved. Do you want to restart the application now to apply the new configuration?',

    // Toast messages
    test_connection_success: 'Connection test successful',
    test_connection_failed: 'Connection test failed',
    config_saved: 'Configuration saved successfully',
    config_save_failed: 'Failed to save configuration',
    restarting_app: 'Application is restarting...',
    restart_success: 'Application restarted successfully',
    restart_failed: 'Failed to restart application',
    manual_restart_required:
      'Please manually restart the application in development environment to avoid white screen issues',
    app_will_close: 'Application will close in 1 second, please restart manually',
    init_failed: 'Failed to initialize middleware settings',
  },
}
