/**
 * 通知模块入口文件
 * 导出所有通知相关的类型、服务和工具
 */

// 基础类型和服务
export {
  type NotificationType,
  type NotificationPosition,
  type NotificationTheme,
  type NotificationAction,
  type BaseNotificationOptions,
  type DesktopNotificationOptions,
  type NotificationSettings,
  type INotificationService,
  BaseNotificationService
} from './notification-service'

// Electron 通知服务
export {
  ElectronNotificationService,
  getNotificationService,
  initNotificationService,
  notify
} from './electron-notification-service'

// 声音通知
export {
  notificationSounds,
  type NotificationSoundType
} from './notification-sounds'
