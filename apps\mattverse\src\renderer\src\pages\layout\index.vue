<template>
  <div class="h-full flex flex-col">
    <SidebarProvider v-model:open="sidebarOpen">
      <AppSidebar />
      <SidebarInset class="flex-1 flex flex-col">
        <!-- Navbar 组件（导航栏） -->
        <Navbar />

        <!-- 主要内容区域，使用 flex-grow 填充剩余空间 -->
        <main class="flex-1 min-h-0 overflow-hidden p-2">
          <div class="h-full rounded-lg bg-muted/50 border border-border">
            <router-view />
          </div>
        </main>
      </SidebarInset>
    </SidebarProvider>

    <!-- Logger Dialog -->
    <LoggerDialog ref="loggerDialogRef" />
  </div>
</template>

<script setup lang="ts">
import AppSidebar from './components/AppSidebar.vue'
import Navbar from './components/Navbar.vue'
import LoggerDialog from '@/pages/base/logger/index.vue'

// 控制侧边栏是否展开的状态，设置为 false 表示默认不展开
const sidebarOpen = ref(false)

// Logger Dialog引用
const loggerDialogRef = ref()

// 暴露方法给子组件调用
const openLoggerDialog = () => {
  loggerDialogRef.value?.openDialog()
}

// 提供给子组件使用
provide('openLoggerDialog', openLoggerDialog)
</script>

<style scoped></style>
