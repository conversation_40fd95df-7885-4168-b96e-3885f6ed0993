<template>
  <Card
    class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
  >
    <CardHeader>
      <CardTitle class="flex items-center space-x-2">
        <MattIcon name="ZoomIn" class="h-5 w-5" />
        <span>{{ $t('settings.flow.zoom.title') }}</span>
      </CardTitle>
      <CardDescription>
        {{ $t('settings.flow.zoom.description') }}
      </CardDescription>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- 最小缩放 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.zoom.min_zoom') }}</Label>
        <div class="space-y-2">
          <Slider
            :model-value="[flowSettings.minZoom]"
            :max="1"
            :min="0.1"
            :step="0.1"
            @update:model-value="updateFlowSettings('minZoom', $event[0])"
          />
          <div class="flex justify-between text-xs text-muted-foreground">
            <span>10%</span>
            <span class="font-medium">{{ Math.round(flowSettings.minZoom * 100) }}%</span>
            <span>100%</span>
          </div>
        </div>
      </div>

      <Separator />

      <!-- 最大缩放 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.zoom.max_zoom') }}</Label>
        <div class="space-y-2">
          <Slider
            :model-value="[flowSettings.maxZoom]"
            :max="5"
            :min="1"
            :step="0.5"
            @update:model-value="updateFlowSettings('maxZoom', $event[0])"
          />
          <div class="flex justify-between text-xs text-muted-foreground">
            <span>100%</span>
            <span class="font-medium">{{ Math.round(flowSettings.maxZoom * 100) }}%</span>
            <span>500%</span>
          </div>
        </div>
      </div>

      <Separator />

      <!-- 默认缩放 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.zoom.default_zoom') }}</Label>
        <div class="space-y-2">
          <Slider
            :model-value="[flowSettings.defaultViewport.zoom]"
            :max="2"
            :min="0.5"
            :step="0.1"
            @update:model-value="updateDefaultViewport('zoom', $event[0])"
          />
          <div class="flex justify-between text-xs text-muted-foreground">
            <span>50%</span>
            <span class="font-medium">{{ Math.round(flowSettings.defaultViewport.zoom * 100) }}%</span>
            <span>200%</span>
          </div>
        </div>
      </div>

      <Separator />

      <!-- 初始化时适应视图 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.flow.zoom.fit_view_on_init') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.flow.zoom.fit_view_on_init_desc') }}
          </p>
        </div>
        <Switch
          :model-value="flowSettings.fitViewOnInit"
          @update:model-value="updateFlowSettings('fitViewOnInit', $event)"
        />
      </div>

      <Separator />

      <!-- 默认视口位置 -->
      <div class="space-y-3">
        <Label class="text-sm">{{ $t('settings.flow.zoom.default_viewport') }}</Label>
        
        <!-- X 位置 -->
        <div class="space-y-2">
          <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.zoom.viewport_x') }}</Label>
          <div class="space-y-2">
            <Slider
              :model-value="[flowSettings.defaultViewport.x]"
              :max="1000"
              :min="-1000"
              :step="50"
              @update:model-value="updateDefaultViewport('x', $event[0])"
            />
            <div class="flex justify-between text-xs text-muted-foreground">
              <span>-1000</span>
              <span class="font-medium">{{ flowSettings.defaultViewport.x }}</span>
              <span>1000</span>
            </div>
          </div>
        </div>

        <!-- Y 位置 -->
        <div class="space-y-2">
          <Label class="text-xs text-muted-foreground">{{ $t('settings.flow.zoom.viewport_y') }}</Label>
          <div class="space-y-2">
            <Slider
              :model-value="[flowSettings.defaultViewport.y]"
              :max="1000"
              :min="-1000"
              :step="50"
              @update:model-value="updateDefaultViewport('y', $event[0])"
            />
            <div class="flex justify-between text-xs text-muted-foreground">
              <span>-1000</span>
              <span class="font-medium">{{ flowSettings.defaultViewport.y }}</span>
              <span>1000</span>
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import type { DefaultViewport, FlowSettingsState } from '@/store'

interface Props {
  flowSettings: FlowSettingsState
  updateFlowSettings: (key: keyof FlowSettingsState, value: any) => void
  updateDefaultViewport: (key: keyof DefaultViewport, value: any) => void
}

defineProps<Props>()
</script>
