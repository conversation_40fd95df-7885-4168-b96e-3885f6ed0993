<template>
  <div
    class="battery-database-node"
    :class="{ selected }"
    :style="{ backgroundColor: nodeBackgroundColor || '#ffffff' }"
    v-bind="$attrs"
  >
    <!-- 节点头部 -->
    <div class="node-header">
      <span class="node-icon">
        <template v-if="data.icon">
          <Icon v-if="data.icon.type === 'icon'" :icon="data.icon.value" class="icon" />
          <img
            v-else-if="data.icon.type === 'svg'"
            :src="data.icon.value"
            class="icon object-contain"
          />
          <img
            v-else-if="data.icon.type === 'image'"
            :src="data.icon.value"
            class="icon object-contain"
          />
        </template>
        <Icon v-else icon="lucide:database" :width="20" :height="20" />
      </span>
      <span class="node-title">{{ data.label || '电池数据库' }}</span>
      <Button
        variant="ghost"
        size="icon"
        class="delete-button h-6 w-6"
        title="删除"
        @click="confirmDelete"
      >
        <Icon icon="lucide:trash-2" :width="14" :height="14" />
      </Button>
    </div>

    <!-- 节点表单 -->
    <Card class="node-form border-none shadow-none">
      <CardContent class="p-3 space-y-3">
        <div class="space-y-1.5">
          <Label for="batteryId" class="text-xs font-medium">电池编号</Label>
          <Input
            id="batteryId"
            v-model="localParams.BatteryId"
            class="h-8 text-sm"
            @change="handleParamsChange"
          />
        </div>

        <div class="space-y-1.5">
          <Label for="cycle" class="text-xs font-medium">数据圈数</Label>
          <div class="flex rounded-md">
            <Button
              variant="outline"
              size="sm"
              class="rounded-r-none px-2 h-8"
              :disabled="localParams.cycle <= 1"
              @click="decrementCycle"
            >
              <Icon icon="lucide:minus" :width="14" :height="14" />
            </Button>
            <Input
              id="cycle"
              v-model="localParams.cycle"
              type="number"
              class="h-8 text-sm text-center rounded-none border-x-0 min-w-0"
              min="1"
              @change="handleParamsChange"
            />
            <Button
              variant="outline"
              size="sm"
              class="rounded-l-none px-2 h-8"
              @click="incrementCycle"
            >
              <Icon icon="lucide:plus" :width="14" :height="14" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 连接点 -->
    <Handle type="target" class="handle handle-target" :position="Position.Left" />
    <Handle type="source" class="handle handle-source" :position="Position.Right" />
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { useNodeTheme } from '../composables/useNodeTheme'
import { useFlowsStore, useSettingsStore } from '@renderer/store'
import emitter from '@renderer/utils/mitt'
import { Handle, Position } from '@vue-flow/core'
import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue'

const flowsStore = useFlowsStore()
const settingsStore = useSettingsStore()
const { getNodeBackgroundColor } = useNodeTheme()

defineOptions({
  inheritAttrs: false,
})

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['delete'])

// 当前主题 - 使用设置存储
const currentTheme = computed(() => settingsStore.theme || 'city-light')

// 本地参数，用于双向绑定
const localParams = ref({
  BatteryId: props.data.params?.BatteryId || '1',
  cycle: props.data.params?.cycle || '100',
})

// 处理参数变化
const handleParamsChange = () => {
  // 确保cycle是字符串类型
  localParams.value.cycle = String(localParams.value.cycle)

  // 保存到节点数据
  if (props.data.workflowId) {
    flowsStore.saveNodeParams(props.data.workflowId, props.id, {
      ...localParams.value,
    })

    // 触发节点数据更新事件
    emitter.emit('node-data-updated', {
      detail: {
        nodeId: props.id,
        data: {
          params: {
            ...localParams.value,
          },
        },
      },
    })
  }
}

// 增加圈数
const incrementCycle = () => {
  const currentValue = parseInt(localParams.value.cycle) || 0
  localParams.value.cycle = String(currentValue + 1)
  handleParamsChange()
}

// 减少圈数
const decrementCycle = () => {
  const currentValue = parseInt(localParams.value.cycle) || 0
  if (currentValue > 1) {
    localParams.value.cycle = String(currentValue - 1)
    handleParamsChange()
  }
}

// 确认删除
const confirmDelete = () => {
  emit('delete')
}

// 计算节点背景色
const nodeBackgroundColor = computed(() => {
  // 如果节点自定义了背景色，优先使用
  if (props.data.backgroundColor) {
    return props.data.backgroundColor
  }

  // 如果没有背景色，则根据节点类型和主题计算
  const nodeType = props.data.nodeType || props.data.type
  const nodeCategory = props.data.category || ''

  return getNodeBackgroundColor(props.id, nodeType, nodeCategory)
})

// 监听参数变化
watch(
  () => props.data.params,
  (newParams) => {
    if (newParams) {
      localParams.value = {
        BatteryId: newParams.BatteryId || '1',
        cycle: newParams.cycle || '100',
      }
    }
  },
  { deep: true },
)

onMounted(() => {
  // 组件挂载时的初始化逻辑
})

onBeforeUnmount(() => {
  // 组件卸载时的清理逻辑
})
</script>

<style lang="scss" scoped>
.battery-database-node {
  @apply bg-card text-card-foreground border border-border rounded-lg shadow-sm p-3 relative;
  width: 280px;
  transition: all 0.2s ease;

  &.selected {
    @apply border-primary ring-1 ring-primary/30;
    transform: translateY(-2px);
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.05),
      0 4px 6px -2px rgba(0, 0, 0, 0.025);
  }

  .node-header {
    @apply flex items-center justify-between w-full mb-2;

    .node-icon {
      @apply flex items-center justify-center p-1.5 rounded-md bg-primary/10 text-primary mr-2;
      width: 32px;
      height: 32px;
      flex-shrink: 0;

      .icon {
        width: 20px;
        height: 20px;
      }
    }

    .node-title {
      @apply flex-1 text-base font-medium;
    }

    .delete-button {
      @apply text-muted-foreground hover:text-destructive;
    }
  }

  .node-form {
    @apply mt-2 border-t pt-2 border-border/50;
  }

  /* 自定义输入框样式 */
  :deep(.input) {
    @apply focus-visible:ring-1 focus-visible:ring-primary/30;
  }

  /* 数字输入框去掉箭头 */
  :deep(input[type='number']) {
    -webkit-appearance: textfield;
    -moz-appearance: textfield;
    appearance: textfield;

    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      -webkit-appearance: none;
      margin: 0;
    }
  }

  :deep(.vue-flow__handle) {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: hsl(var(--background));
    border: 2px solid hsl(var(--primary));
    transition: all 0.2s ease;
    position: absolute;
    z-index: 1;

    // 输入节点样式（target）
    &.handle-target {
      border: 2px solid hsl(var(--primary));
      background: hsl(var(--background));

      &::after {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border-radius: 50%;
        border: 2px solid transparent;
        transition: all 0.2s ease;
      }

      &:hover::after {
        border-color: hsl(var(--primary));
        transform: scale(1.2);
      }
    }

    // 输出节点样式（source）
    &.handle-source {
      border: none;
      background: hsl(var(--primary));

      &::after {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border-radius: 50%;
        background: transparent;
        transition: all 0.2s ease;
      }

      &:hover::after {
        background: hsl(var(--primary));
        opacity: 0.3;
        transform: scale(1.2);
      }
    }
  }

  :deep(.vue-flow__handle-left) {
    left: -6px;
  }

  :deep(.vue-flow__handle-right) {
    right: -6px;
  }
}

/* 暗黑模式适配 */
.dark .battery-database-node {
  @apply bg-card text-card-foreground border-border;

  &.selected {
    @apply border-primary ring-1 ring-primary/30;
  }
}
</style>
