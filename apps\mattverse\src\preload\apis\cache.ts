/**
 * 缓存相关 API
 */
import { ipcRenderer } from 'electron'
import type { CacheType, CacheInfo } from '@mattverse/electron-core'

/**
 * 缓存管理 API
 */
export const cacheAPI = {
  /**
   * 获取所有缓存信息
   */
  getCacheInfo: (): Promise<Record<string, CacheInfo>> => ipcRenderer.invoke('cache:get-info'),

  /**
   * 清理指定类型的缓存
   */
  clearCacheTypes: (types: CacheType[]): Promise<{ success: boolean; cleared: CacheType[] }> =>
    ipcRenderer.invoke('cache:clear-types', types),

  /**
   * 清理所有缓存
   */
  clearAllCache: (): Promise<{ success: boolean }> => ipcRenderer.invoke('cache:clear-all'),

  /**
   * 设置缓存大小限制
   */
  setCacheSizeLimit: (type: CacheType, sizeMB: number): Promise<{ success: boolean }> =>
    ipcRenderer.invoke('cache:set-size-limit', type, sizeMB),

  /**
   * 执行自动清理
   */
  autoClean: (settings: {
    enabled: boolean
    interval: 'daily' | 'weekly' | 'monthly' | 'never'
    maxAge: number
    enabledTypes: CacheType[]
  }): Promise<{ success: boolean; cleaned: CacheType[] }> =>
    ipcRenderer.invoke('cache:auto-clean', settings),

  /**
   * 应用退出时清理缓存
   */
  clearOnExit: (enabledTypes: CacheType[]): Promise<{ success: boolean }> =>
    ipcRenderer.invoke('cache:clear-on-exit', enabledTypes),

  /**
   * 更新缓存管理器设置
   */
  updateManagerSettings: (settings: {
    enableCache: boolean
    clearOnExit: boolean
    autoClean: {
      enabled: boolean
      interval: 'daily' | 'weekly' | 'monthly' | 'never'
      maxAge: number
    }
    types: Record<string, { enabled: boolean; maxSize: number; autoClean: boolean }>
  }): Promise<{ success: boolean }> =>
    ipcRenderer.invoke('cache:update-manager-settings', settings),
}
