<template>
  <div
    v-show="taskId && taskStatus !== 'TaskStay'"
    :class="$attrs.class"
    class="max-w-2xl mx-auto bg-background rounded-lg shadow-sm border p-4 my-2"
  >
    <!-- 添加节点ID标识 -->
    <div class="hidden" :data-node-id="nodeId"></div>
    <!-- Header with status and task ID -->
    <div class="flex flex-col space-y-2">
      <!-- Task ID 显示 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <span class="text-gray-600 text-[10px] mr-1">任务ID:</span>
          <span
            :class="
              cn('text-foreground text-[10px] truncate font-mono bg-muted px-1 rounded', {
                'max-w-[160px]': !expanded,
                'max-w-full': expanded,
              })
            "
          >
            {{ taskId }}
          </span>
          <CopyIcon
            class="h-3 w-3 ml-1 text-muted-foreground cursor-pointer hover:text-foreground"
            @click="copyToClipboard(taskId)"
          />
        </div>
      </div>

      <!-- Status 显示 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div :class="getStatusIconBgClass(taskStatus)" class="p-1 rounded-full mr-2">
            <component
              :is="getStatusIcon(taskStatus)"
              class="h-3 w-3"
              :class="[
                getStatusIconClass(taskStatus),
                taskStatus === 'Computing' || taskStatus === 'Pending' ? 'animate-spin' : '',
              ]"
            />
          </div>
          <span
            :class="getStatusBadgeClass(taskStatus)"
            class="text-gray-800 text-[10px] mr-2 py-0.5 px-1 rounded"
          >
            {{ getStatusText(taskStatus) }}
          </span>
          <span class="text-[10px] px-1 rounded bg-muted text-foreground">
            {{ executionTime || '0s' }}
          </span>
        </div>
        <div class="flex items-center text-muted-foreground text-[10px]">
          <span @click="expanded = !expanded">{{ expanded ? '收起结果' : '展开结果' }}</span>
          <component
            :is="expanded ? ChevronUpIcon : ChevronDownIcon"
            class="h-5 w-5 ml-1 cursor-pointer hover:text-foreground"
            @click="expanded = !expanded"
          />
        </div>
      </div>
    </div>

    <!-- 展开的内容 -->
    <div v-if="expanded">
      <!-- 进度条 -->
      <div
        v-if="
          taskStatus === 'Computing' || taskStatus === 'Initializing' || taskStatus === 'Pending'
        "
        class="mb-4 mt-4"
      >
        <div class="flex justify-between mb-2">
          <span class="text-sm font-medium text-foreground">任务进度</span>
          <span class="text-sm font-medium text-primary">
            {{ (taskProcess && taskProcess.toFixed(2)) || 0 }} %
          </span>
        </div>
        <div class="w-full bg-muted rounded-full h-2.5">
          <div
            class="bg-primary h-2.5 rounded-full"
            :style="{ width: `${taskProcess || 0}%` }"
          ></div>
        </div>
      </div>

      <!-- Input section - 显示 submitService 方法的入参 -->
      <div v-if="false" class="my-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-foreground font-medium">输入参数</span>
          <div class="flex items-center space-x-2">
            <CopyIcon
              class="h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground"
              @click="copyToClipboard(inputData)"
            />
            <Button
              variant="outline"
              size="sm"
              class="h-6 px-2 py-1 text-xs flex items-center"
              @click="openJsonViewer('input')"
            >
              <SearchIcon class="h-3 w-3 mr-1" />
              查看详情
            </Button>
          </div>
        </div>
        <div class="bg-muted rounded-lg p-3 max-h-40 overflow-auto scrollbar">
          <div
            v-if="Object.keys(inputData).length === 0"
            class="text-muted-foreground text-xs italic text-center py-2"
          >
            暂无输入参数
          </div>
          <div v-else class="grid grid-cols-1 gap-1">
            <div
              v-for="(value, key) in getPreviewData(inputData)"
              :key="key"
              class="flex items-start py-1 px-2 rounded hover:bg-muted/50 transition-colors"
            >
              <span class="text-xs font-medium text-indigo-500 mr-1">{{ key }}:</span>
              <span class="text-xs text-purple-600 break-all">{{ formatPreviewValue(value) }}</span>
            </div>
            <div
              v-if="Object.keys(inputData).length > 3"
              class="text-xs text-center text-blue-500 cursor-pointer hover:underline mt-1"
              @click="openJsonViewer('input')"
            >
              查看全部 {{ Object.keys(inputData).length }} 个参数...
            </div>
          </div>
        </div>
      </div>

      <!-- Output section - 显示 getTaskResult 返回的结果 -->
      <div v-if="outputData" class="my-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-foreground font-medium">计算结果</span>
          <div class="flex items-center space-x-2">
            <CopyIcon
              class="h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground"
              @click="copyToClipboard(outputData)"
            />
            <Button
              variant="outline"
              size="sm"
              class="h-6 px-2 py-1 text-xs flex items-center"
              @click="openJsonViewer('output')"
            >
              <SearchIcon class="h-3 w-3 mr-1" />
              查看详情
            </Button>
          </div>
        </div>
        <div class="bg-muted rounded-lg p-3 max-h-40 overflow-y-auto scrollbar">
          <div v-if="!outputData" class="text-muted-foreground text-xs italic text-center py-2">
            暂无计算结果
          </div>
          <div v-else class="relative">
            <pre class="text-xs font-mono whitespace-pre-wrap break-all">
            <code class="language-json" v-html="getPreviewOutput(outputData)"></code>
            </pre>
            <div
              v-if="isOutputTruncated"
              class="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-muted to-transparent flex items-end justify-center"
            >
              <span
                class="text-xs text-blue-500 cursor-pointer hover:underline"
                @click="openJsonViewer('output')"
              >
                查看完整结果...
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- JSON 查看器弹窗 -->
  <MattJsonViewer v-model:is-open="jsonViewerOpen" :initial-data="jsonViewerData" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { toast } from 'vue-sonner'
import { useDebounceFn } from '@vueuse/core'

import { MattJsonViewer } from '@mattverse/mattverse-ui'
import { type Task, TaskStatus } from '@mattverse/shared'

import {
  Ban,
  BanIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  CircleCheckIcon,
  CircleXIcon,
  CopyIcon,
  EllipsisIcon,
  InfoIcon,
  LoaderIcon,
  SearchIcon,
} from 'lucide-vue-next'

const expanded = ref(false)
const jsonViewerOpen = ref(false)
const jsonViewerData = ref(null)
const isOutputTruncated = ref(false)
// 确保组件可以接收任意属性
defineOptions({
  inheritAttrs: false,
})

// 接收父组件传递的属性
const props = defineProps({
  nodeId: {
    type: String,
    required: true,
  },
  taskId: {
    type: String,
    default: '',
  },
  taskStatus: {
    type: String as () => TaskStatus,
    default: 'TaskStay',
  },
  taskProcess: {
    type: Number,
    default: 0,
  },
  executionTime: {
    type: String,
    default: '0s',
  },
  inputData: {
    type: Object,
    default: () => ({}),
  },
  outputData: {
    type: [Object, String],
    default: '',
  },
})

// 获取预览数据（最多显示前3个键值对）
const getPreviewData = data => {
  const keys = Object.keys(data)
  if (keys.length <= 3) return data

  const previewData = {}
  keys.slice(0, 3).forEach(key => {
    previewData[key] = data[key]
  })
  return previewData
}

// 格式化预览值（截断过长的值）
const formatPreviewValue = value => {
  if (value === null || value === undefined) return 'null'

  const strValue = String(value)
  if (strValue.length > 50) {
    return `"${strValue.substring(0, 50)}..."`
  }
  return `"${strValue}"`
}

// 获取输出预览（限制长度）
const getPreviewOutput = data => {
  if (!data) return ''

  try {
    let formattedData = ''

    // 如果是字符串形式的JSON，尝试解析并格式化
    if (typeof data === 'string' && (data.startsWith('{') || data.startsWith('['))) {
      formattedData = JSON.stringify(JSON.parse(data), null, 2)
    }
    // 如果是对象，直接格式化
    else if (typeof data === 'object') {
      formattedData = JSON.stringify(data, null, 2)
    }
    // 其他情况直接返回
    else {
      formattedData = String(data)
    }

    // 检查是否需要截断
    if (formattedData.length > 500) {
      isOutputTruncated.value = true
      return formattedData.substring(0, 500) + '...'
    }

    isOutputTruncated.value = false
    return formattedData
  } catch (e) {
    console.error('格式化输出数据失败:', e)
    return String(data)
  }
}

// 格式化输出数据（完整版本，用于JSON查看器）
const formatOutputData = data => {
  if (!data) return ''

  try {
    // 如果是字符串形式的JSON，尝试解析并格式化
    if (typeof data === 'string' && (data.startsWith('{') || data.startsWith('['))) {
      return JSON.parse(data)
    }
    // 如果是对象，直接返回
    else if (typeof data === 'object') {
      return data
    }
    // 其他情况尝试解析
    try {
      return JSON.parse(data)
    } catch {
      return data
    }
  } catch (e) {
    console.error('格式化输出数据失败:', e)
    return data
  }
}

// 打开JSON查看器
const openJsonViewer = type => {
  if (type === 'input') {
    jsonViewerData.value = props.inputData
  } else {
    jsonViewerData.value = formatOutputData(props.outputData)
  }
  jsonViewerOpen.value = true
}

// 获取状态对应的图标
const getStatusIcon = status => {
  const icons = {
    Initializing: InfoIcon,
    Computing: LoaderIcon,
    Pending: LoaderIcon,
    Paused: BanIcon,
    Finished: CircleCheckIcon,
    Error: CircleXIcon,
    TaskStay: EllipsisIcon,
    Abort: Ban,
  }
  return icons[status] || EllipsisIcon
}

// 获取状态对应的图标背景色
const getStatusIconBgClass = status => {
  const classes = {
    Initializing: 'bg-blue-100 dark:bg-blue-900/20',
    Computing: 'bg-blue-100 dark:bg-blue-900/20',
    Pending: 'bg-yellow-100 dark:bg-yellow-900/20',
    Paused: 'bg-orange-100 dark:bg-orange-900/20',
    Finished: 'bg-green-100 dark:bg-green-900/20',
    Error: 'bg-red-100 dark:bg-red-900/20',
    TaskStay: 'bg-gray-100 dark:bg-gray-800',
    Abort: 'bg-red-100 dark:bg-red-900/20',
  }
  return classes[status] || 'bg-gray-100'
}

// 获取状态对应的图标颜色
const getStatusIconClass = status => {
  const classes = {
    Initializing: 'text-blue-500 dark:text-blue-400',
    Computing: 'text-blue-500 dark:text-blue-400',
    Pending: 'text-yellow-500 dark:text-yellow-400',
    Paused: 'text-orange-500 dark:text-orange-400',
    Finished: 'text-green-500 dark:text-green-400',
    Error: 'text-red-500 dark:text-red-400',
    TaskStay: 'text-gray-500 dark:text-gray-400',
    Abort: 'text-red-500 dark:text-red-400',
  }
  return classes[status] || 'text-gray-500'
}

// 获取状态对应的徽章背景色
const getStatusBadgeClass = status => {
  const classes = {
    Initializing: 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400',
    Computing: 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400',
    Pending: 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400',
    Paused: 'bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400',
    Finished: 'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400',
    Error: 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400',
    TaskStay: 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400',
    Abort: 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400',
  }
  return classes[status] || 'bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400'
}

// 获取状态显示文本
const getStatusText = status => {
  const statusText = {
    Initializing: '初始化中',
    Computing: '计算中',
    Pending: '等待调度',
    Paused: '暂停',
    Finished: '任务完成',
    Error: '任务失败',
    TaskStay: '原始状态',
    Abort: '已终止',
  }
  return statusText[status] || status
}

// 复制到剪贴板
const debouncedToast = useDebounceFn(() => {
  toast.success('操作成功', {
    description: '已复制到剪贴板！',
    duration: 3000,
  })
}, 300)

// 复制到剪贴板
const copyToClipboard = data => {
  const text = typeof data === 'object' ? JSON.stringify(data, null, 2) : String(data)
  navigator.clipboard
    .writeText(text)
    .then(() => {
      debouncedToast()
    })
    .catch(err => {
      console.error('复制失败:', err)
    })
}
</script>

<style scoped>
pre {
  background: #1e1e1e;
  padding: 1rem;
  border-radius: 0.5rem;
  font-family: 'Fira Code', monospace;
}

code {
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
}
</style>
