// electron.vite.config.ts
import { defineConfig } from "electron-vite";
import { resolve } from "path";
import vue from "@vitejs/plugin-vue";
import tailwindcss from "@tailwindcss/vite";
import { autoImportPresets, createCopyProtosPlugin } from "@mattverse/configs";
var __electron_vite_injected_dirname = "C:\\myFile\\workspaces\\aliyun\\dev\\mattverse-vite\\apps\\mattverse";
var isDev = process.env.NODE_ENV === "development";
var isProduction = process.env.NODE_ENV === "production";
var electron_vite_config_default = defineConfig({
  main: {
    resolve: {
      alias: {
        "@": resolve(__electron_vite_injected_dirname, "src/main"),
        "@core": resolve(__electron_vite_injected_dirname, "../../packages/electron-core/src")
      }
    },
    plugins: [
      createCopyProtosPlugin({
        sourceDir: "../../packages/electron-core/src/grpc/protos",
        targetDir: "out/main/protos",
        files: "all",
        verbose: true
      })
    ],
    build: {
      rollupOptions: {
        external: ["@grpc/grpc-js", "@grpc/proto-loader"]
      }
    }
  },
  preload: {
    resolve: {
      alias: {
        "@": resolve(__electron_vite_injected_dirname, "src/preload"),
        "@core": resolve(__electron_vite_injected_dirname, "../../packages/electron-core/src")
      }
    },
    build: {
      rollupOptions: {
        external: ["protobufjs", "@grpc/grpc-js", "@grpc/proto-loader"]
      }
    }
  },
  renderer: {
    plugins: [
      vue({
        // Vue 插件优化
        script: {
          defineModel: true,
          //启用 defineModel 宏
          propsDestructure: true
          // 启用 props 解构
        }
      }),
      tailwindcss(),
      ...autoImportPresets.default("mattverse")
    ],
    resolve: {
      alias: {
        "@": resolve(__electron_vite_injected_dirname, "src/renderer/src"),
        "@ui": resolve(__electron_vite_injected_dirname, "../../packages/mattverse-ui/src"),
        "@flow": resolve(__electron_vite_injected_dirname, "../../packages/mattverse-flow/src"),
        "@configs": resolve(__electron_vite_injected_dirname, "../../packages/configs/src"),
        "@shared": resolve(__electron_vite_injected_dirname, "../../packages/shared/src")
      }
    },
    publicDir: resolve(__electron_vite_injected_dirname, "../../packages/shared/src/assets"),
    assetsInclude: ["**/*.ttf", "**/*.woff", "**/*.woff2"],
    server: {
      fs: {
        // 允许访问 workspace 根目录，支持 monorepo 结构
        allow: ["../.."]
      },
      // 开发服务器配置
      hmr: {
        overlay: true
        // 显示错误覆盖层
      }
    },
    optimizeDeps: {
      include: [
        // 核心依赖
        "vue",
        "vue-router",
        "@vueuse/core",
        // UI 组件库
        "lucide-vue-next",
        "radix-vue",
        // Workspace 包
        "@mattverse/shared",
        "@mattverse/mattverse-ui",
        "@mattverse/mattverse-flow",
        "@mattverse/i18n"
      ],
      exclude: [
        // 排除不需要预构建的包
        "electron",
        "@electron/remote"
      ],
      // 只在开发环境强制重新构建，避免生产环境性能问题
      force: isDev
    },
    build: {
      // 构建优化
      target: "esnext",
      minify: "esbuild",
      sourcemap: isDev,
      rollupOptions: {
        output: {
          // 代码分割优化
          manualChunks: {
            "vue-vendor": ["vue", "vue-router"],
            "ui-vendor": ["radix-vue", "lucide-vue-next"],
            "utils-vendor": ["@vueuse/core"],
            "mattverse-vendor": [
              "@mattverse/shared",
              "@mattverse/mattverse-ui",
              "@mattverse/mattverse-flow",
              "@mattverse/i18n"
            ]
          },
          // 优化文件名
          chunkFileNames: "js/[name]-[hash].js",
          entryFileNames: "js/[name]-[hash].js",
          assetFileNames: (assetInfo) => {
            const fileName = assetInfo.names?.[0] || assetInfo.name || "unknown";
            if (/\.(css)$/.test(fileName)) {
              return "css/[name]-[hash].[ext]";
            }
            if (/\.(png|jpe?g|gif|svg|webp|ico)$/.test(fileName)) {
              return "images/[name]-[hash].[ext]";
            }
            if (/\.(woff2?|eot|ttf|otf)$/.test(fileName)) {
              return "fonts/[name]-[hash].[ext]";
            }
            return "assets/[name]-[hash].[ext]";
          }
        }
      }
    }
  }
});
export {
  electron_vite_config_default as default
};
