/**
 * 验证工具函数
 */
import type { WorkflowNode, WorkflowEdge } from '../types'

/**
 * 验证节点ID是否唯一
 */
export function validateUniqueNodeIds(nodes: WorkflowNode[]): boolean {
  const ids = nodes.map(node => node.id)
  return ids.length === new Set(ids).size
}

/**
 * 验证边ID是否唯一
 */
export function validateUniqueEdgeIds(edges: WorkflowEdge[]): boolean {
  const ids = edges.map(edge => edge.id)
  return ids.length === new Set(ids).size
}

/**
 * 验证边的连接是否有效
 */
export function validateEdgeConnections(nodes: WorkflowNode[], edges: WorkflowEdge[]): boolean {
  const nodeIds = new Set(nodes.map(node => node.id))

  return edges.every(edge => nodeIds.has(edge.source) && nodeIds.has(edge.target))
}

/**
 * 验证是否存在循环依赖
 */
export function validateNoCycles(nodes: WorkflowNode[], edges: WorkflowEdge[]): boolean {
  const graph = new Map<string, string[]>()

  // 构建邻接表
  nodes.forEach(node => graph.set(node.id, []))
  edges.forEach(edge => {
    const neighbors = graph.get(edge.source) || []
    neighbors.push(edge.target)
    graph.set(edge.source, neighbors)
  })

  // DFS检测循环
  const visited = new Set<string>()
  const recursionStack = new Set<string>()

  function hasCycle(nodeId: string): boolean {
    if (recursionStack.has(nodeId)) return true
    if (visited.has(nodeId)) return false

    visited.add(nodeId)
    recursionStack.add(nodeId)

    const neighbors = graph.get(nodeId) || []
    for (const neighbor of neighbors) {
      if (hasCycle(neighbor)) return true
    }

    recursionStack.delete(nodeId)
    return false
  }

  for (const nodeId of graph.keys()) {
    if (!visited.has(nodeId) && hasCycle(nodeId)) {
      return false
    }
  }

  return true
}

/**
 * 验证节点位置是否有效
 */
export function validateNodePositions(nodes: WorkflowNode[]): boolean {
  return nodes.every(
    node =>
      typeof node.position.x === 'number' &&
      typeof node.position.y === 'number' &&
      !isNaN(node.position.x) &&
      !isNaN(node.position.y)
  )
}
