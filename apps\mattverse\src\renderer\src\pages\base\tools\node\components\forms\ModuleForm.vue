<template>
  <Form class="space-y-6" @submit="onSubmit">
    <div class="space-y-2">
      <h3 class="text-sm font-medium text-muted-foreground">基础信息</h3>
      <Separator />
    </div>
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-12 md:col-span-6">
        <FormField v-slot="{ componentField }" name="name">
          <FormItem>
            <FormLabel>模块名称</FormLabel>
            <FormControl>
              <Input placeholder="模块名称" v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </div>

      <div class="col-span-12 md:col-span-6">
        <FormField v-slot="{ componentField }" name="type">
          <FormItem>
            <FormLabel>模块类型</FormLabel>
            <FormControl>
              <Input placeholder="类型标识" v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </div>

      <div class="col-span-12">
        <FormField v-slot="{ componentField }" name="description">
          <FormItem>
            <FormLabel>描述</FormLabel>
            <FormControl>
              <Textarea rows="3" placeholder="描述..." v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </div>

      <div class="col-span-12 md:col-span-6">
        <FormField v-slot="{ componentField }" name="sort">
          <FormItem>
            <FormLabel>排序</FormLabel>
            <FormControl>
              <Input type="number" placeholder="0" v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </div>

      <div class="col-span-12 md:col-span-6">
        <FormField v-slot="{ componentField }" name="enabled">
          <FormItem>
            <div class="flex items-center gap-3 pt-6">
              <FormControl>
                <Switch v-bind="componentField" />
              </FormControl>
              <FormLabel class="text-sm font-normal">是否启用</FormLabel>
            </div>
          </FormItem>
        </FormField>
      </div>
    </div>

    <div class="space-y-2">
      <h3 class="text-sm font-medium text-muted-foreground">图标与状态</h3>
      <Separator />
    </div>
    <div class="grid grid-cols-12 gap-4">
      <div class="col-span-12 md:col-span-6">
        <FormField v-slot="{ componentField }" name="iconType">
          <FormItem>
            <FormLabel>图标类型</FormLabel>
            <FormControl>
              <Select v-bind="componentField">
                <SelectTrigger>
                  <SelectValue placeholder="选择图标类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="icon">Icon</SelectItem>
                  <SelectItem value="svg">SVG</SelectItem>
                </SelectContent>
              </Select>
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </div>

      <div class="col-span-12 md:col-span-6">
        <FormField v-slot="{ componentField }" name="iconValue">
          <FormItem>
            <FormLabel>图标值</FormLabel>
            <FormControl>
              <Input placeholder="如 Battery / materialCalculation" v-bind="componentField" />
            </FormControl>
            <FormMessage />
          </FormItem>
        </FormField>
      </div>
    </div>

    <div v-if="!props.hideActions" class="flex justify-end gap-2">
      <slot name="left-actions" />
      <Button type="button" variant="outline" @click="onReset">重置</Button>
      <Button type="submit">保存</Button>
    </div>
  </Form>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { z } from 'zod'
import { watch } from 'vue'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@mattverse/mattverse-ui'

const props = withDefaults(
  defineProps<{
    hideActions?: boolean
  }>(),
  {
    hideActions: false,
  }
)

const model = defineModel<{
  name: string
  type: string
  description: string
  sort?: number
  enabled: boolean
  iconType: 'svg' | 'icon'
  iconValue: string
}>()
const emit = defineEmits<{ (e: 'save'): void; (e: 'reset'): void }>()

const schema = z.object({
  name: z.string().min(1, '请输入模块名称'),
  type: z.string().min(1, '请输入模块类型'),
  description: z.string().optional(),
  sort: z.number({ invalid_type_error: '排序需为数字' }).min(0, '排序需为非负数').optional(),
  enabled: z.boolean(),
  iconType: z.enum(['svg', 'icon'], { required_error: '请选择图标类型' }),
  iconValue: z.string().optional(),
})

const { handleSubmit, resetForm, values } = useForm({
  validationSchema: toTypedSchema(schema),
  initialValues: { ...(model.value as any) },
})

// 监听 model 变化，同步到表单（单向同步，避免循环）
watch(
  () => model.value,
  newModel => {
    if (newModel) {
      // 重置表单为新的值
      resetForm({ values: { ...(newModel as any) } })
    }
  },
  { deep: true, immediate: true }
)

// 在表单提交时同步到 model
const syncToModel = () => {
  if (model.value) {
    Object.assign(model.value as any, values)
  }
}

function onReset() {
  resetForm({ values: { ...(model.value as any) } })
  emit('reset')
}

const onSubmit = handleSubmit(() => {
  syncToModel()
  emit('save')
})
</script>

<style scoped></style>
