/**
 * 对话框相关 API
 */
import { ipcRenderer } from 'electron'

/**
 * 文件对话框选项
 */
export interface FileDialogOptions {
  title?: string
  defaultPath?: string
  buttonLabel?: string
  filters?: Array<{
    name: string
    extensions: string[]
  }>
}

/**
 * 保存对话框选项
 */
export interface SaveDialogOptions {
  title?: string
  defaultPath?: string
  buttonLabel?: string
  filters?: Array<{
    name: string
    extensions: string[]
  }>
}

/**
 * 对话框 API
 */
export const dialogAPI = {
  /**
   * 选择文件
   */
  selectFile: (options?: FileDialogOptions): Promise<string | null> =>
    ipcRenderer.invoke('dialog:select-file', options),

  /**
   * 选择文件夹
   */
  selectFolder: (): Promise<string | null> =>
    ipcRenderer.invoke('dialog:select-folder'),

  /**
   * 保存文件
   */
  saveFile: (options?: SaveDialogOptions): Promise<string | null> =>
    ipcRenderer.invoke('dialog:save-file', options),

  /**
   * 显示消息
   */
  showMessage: (message: string, type?: 'info' | 'warning' | 'error'): Promise<void> =>
    ipcRenderer.invoke('dialog:show-message', message, type),
}
