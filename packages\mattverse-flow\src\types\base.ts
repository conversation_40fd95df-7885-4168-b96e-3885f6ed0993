/**
 * 基础类型定义
 */

/**
 * 位置信息
 */
export interface Position {
  x: number
  y: number
}

/**
 * 尺寸信息
 */
export interface Dimensions {
  width: number
  height: number
}

/**
 * 视口信息
 */
export interface Viewport {
  x: number
  y: number
  zoom: number
}

/**
 * 连接信息
 */
export interface Connection {
  source: string
  target: string
  sourceHandle?: string | null
  targetHandle?: string | null
  type?: string
}

/**
 * 节点变更类型
 */
export type NodeChangeType = 'add' | 'remove' | 'select' | 'position' | 'dimensions'

/**
 * 边变更类型
 */
export type EdgeChangeType = 'add' | 'remove' | 'select'

/**
 * 节点变更
 */
export interface NodeChange {
  id: string
  type: NodeChangeType
  selected?: boolean
  position?: Position
  dimensions?: Dimensions
  data?: any
}

/**
 * 边变更
 */
export interface EdgeChange {
  id: string
  type: EdgeChangeType
  selected?: boolean
  data?: any
}
