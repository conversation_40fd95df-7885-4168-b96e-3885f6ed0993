<template>
  <Card
    class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
  >
    <CardHeader>
      <CardTitle class="flex items-center space-x-2">
        <MattIcon name="Play" class="h-5 w-5" />
        <span>{{ $t('settings.flow.execution.title') }}</span>
      </CardTitle>
      <CardDescription>
        {{ $t('settings.flow.execution.description') }}
      </CardDescription>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- 执行模式 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.execution.mode') }}</Label>
        <Select
          :model-value="flowSettings.executionMode"
          @update:model-value="updateFlowSettings('executionMode', $event)"
        >
          <SelectTrigger>
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="mode in executionModes"
              :key="mode.value"
              :value="mode.value"
            >
              {{ mode.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Separator />

      <!-- 最大并发节点数 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.execution.max_concurrent') }}</Label>
        <div class="space-y-2">
          <Slider
            :model-value="[flowSettings.maxConcurrentNodes]"
            :max="20"
            :min="1"
            :step="1"
            @update:model-value="updateFlowSettings('maxConcurrentNodes', $event[0])"
          />
          <div class="flex justify-between text-xs text-muted-foreground">
            <span>1</span>
            <span class="font-medium">{{ flowSettings.maxConcurrentNodes }}</span>
            <span>20</span>
          </div>
        </div>
      </div>

      <Separator />

      <!-- 执行超时 -->
      <div class="space-y-2">
        <Label class="text-sm">{{ $t('settings.flow.execution.timeout') }} ({{ $t('common.seconds') }})</Label>
        <div class="space-y-2">
          <Slider
            :model-value="[flowSettings.executionTimeout]"
            :max="3600"
            :min="30"
            :step="30"
            @update:model-value="updateFlowSettings('executionTimeout', $event[0])"
          />
          <div class="flex justify-between text-xs text-muted-foreground">
            <span>30s</span>
            <span class="font-medium">{{ flowSettings.executionTimeout }}s</span>
            <span>1h</span>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from '@mattverse/i18n'
import type { FlowExecutionMode, FlowSettingsState } from '@/store'

interface Props {
  flowSettings: FlowSettingsState
  updateFlowSettings: (key: keyof FlowSettingsState, value: any) => void
}

defineProps<Props>()

const { t } = useI18n()

// 选项数据
const executionModes = computed(() => [
  { value: 'sequential' as FlowExecutionMode, label: t('settings.flow.execution.modes.sequential') },
  { value: 'parallel' as FlowExecutionMode, label: t('settings.flow.execution.modes.parallel') },
  { value: 'mixed' as FlowExecutionMode, label: t('settings.flow.execution.modes.mixed') },
])
</script>
