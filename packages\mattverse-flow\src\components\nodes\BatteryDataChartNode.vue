<template>
  <div
    class="battery-chart-node"
    :class="{ selected }"
    :style="{ backgroundColor: nodeBackgroundColor || '#ffffff' }"
    v-bind="$attrs"
  >
    <!-- 节点头部 -->
    <div class="node-header">
      <span class="node-icon">
        <template v-if="data.icon">
          <Icon v-if="data.icon.type === 'icon'" :icon="data.icon.value" class="icon" />
          <img
            v-else-if="data.icon.type === 'svg'"
            :src="data.icon.value"
            class="icon object-contain"
          />
          <img
            v-else-if="data.icon.type === 'image'"
            :src="data.icon.value"
            class="icon object-contain"
          />
        </template>
        <Icon v-else icon="lucide:line-chart" :width="20" :height="20" />
      </span>
      <span class="node-title">{{ data.label || '电池数据图表' }}</span>
      <Button
        variant="ghost"
        size="icon"
        class="delete-button h-6 w-6"
        title="删除"
        @click="confirmDelete"
      >
        <Icon icon="lucide:trash-2" :width="14" :height="14" />
      </Button>
    </div>

    <!-- 图表容器 -->
    <Card class="chart-card border-none shadow-none">
      <CardContent class="p-3 relative">
        <!-- 图表容器 -->
        <div ref="chartRef" class="echarts-container"></div>

        <!-- 无数据提示，使用绝对定位覆盖在图表上 -->
        <div v-if="!chartData.length" class="no-data-overlay">
          <div class="flex flex-col items-center justify-center space-y-2">
            <Icon
              icon="lucide:bar-chart-2"
              class="text-muted-foreground"
              :width="32"
              :height="32"
            />
            <p class="text-sm text-muted-foreground">等待数据输入...</p>
            <Badge variant="outline" class="text-xs font-normal">
              <Icon icon="lucide:arrow-left" :width="12" :height="12" class="mr-1" />
              请连接电池数据库节点
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 连接点 -->
    <Handle type="target" class="handle handle-target" :position="Position.Left" />
  </div>
</template>

<script setup lang="ts">
import { Icon } from '@iconify/vue'
import { useNodeTheme } from '../composables/useNodeTheme'
import { useFlowsStore, useSettingsStore } from '@renderer/store'
import emitter from '@renderer/utils/mitt'
import { Handle, Position } from '@vue-flow/core'
import * as echarts from 'echarts'
import { computed, nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'

defineOptions({
  inheritAttrs: false,
})

const flowsStore = useFlowsStore()
const settingsStore = useSettingsStore()
const { getNodeBackgroundColor, isDarkMode } = useNodeTheme()

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
  data: {
    type: Object,
    required: true,
  },
  selected: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['delete'])

// 当前主题 - 使用设置存储
const currentTheme = computed(() => settingsStore.theme || 'city-light')
const chartRef = ref(null)
const chartInstance = ref(null)
const chartData = ref([])

// 确认删除
const confirmDelete = () => {
  emit('delete')
}

const chartAxisNames = ref({
  xAxis: '循环次数',
  yAxis: '容量 (%)',
})

// 从节点自身的params获取数据
const loadDataFromNodeParams = () => {
  if (!props.data || !props.data.params || !props.data.params.data) {
    return
  }

  try {
    // 创建一个安全的数据副本，避免直接传递可能包含不可序列化对象的原始数据
    const safeData = {
      params: {
        data: props.data.params.data,
      },
    }
    updateChartData(safeData)
  } catch (error) {
    console.error('加载节点数据失败:', error.message)
  }
}

// 更新图表数据
const updateChartData = (sourceNodeData) => {
  if (!sourceNodeData || !sourceNodeData.params) {
    return
  }

  try {
    // 清空现有数据
    chartData.value = []

    // 检查是否有 data 数组
    const paramsData = sourceNodeData.params.data
    if (paramsData && Array.isArray(paramsData) && paramsData.length >= 2) {
      // 获取 x 轴和 y 轴的键名
      const xAxisKey = Object.keys(paramsData[0])[0] // 例如 "a"
      const yAxisKey = Object.keys(paramsData[1])[0] // 例如 "b"

      if (!xAxisKey || !yAxisKey) {
        return
      }

      // 获取 x 轴和 y 轴的数据数组
      const xAxisData = paramsData[0][xAxisKey]
      const yAxisData = paramsData[1][yAxisKey]

      if (!Array.isArray(xAxisData) || !Array.isArray(yAxisData)) {
        return
      }

      // 组合成图表所需的数据格式
      if (xAxisData.length === yAxisData.length && xAxisData.length > 0) {
        // 创建简单数据数组，避免复杂对象引用
        const simpleData = []
        for (let i = 0; i < xAxisData.length; i++) {
          const x = Number(xAxisData[i])
          const y = Number(yAxisData[i])
          if (!isNaN(x) && !isNaN(y)) {
            // 使用基本数值类型，避免对象引用问题
            simpleData.push([x, y])
          }
        }

        // 直接赋值，不使用JSON序列化
        chartData.value = simpleData

        // 设置图表的轴标题
        chartAxisNames.value = {
          xAxis: xAxisKey,
          yAxis: yAxisKey,
        }

        // 立即初始化图表
        nextTick(() => {
          // 确保DOM已更新后再初始化图表
          if (chartRef.value) {
            initChart()
          }
        })
      }
    }
  } catch (error) {
    console.error('解析图表数据失败:', error.message, error.stack)
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) {
    return
  }

  try {
    // 销毁旧实例
    if (chartInstance.value) {
      chartInstance.value.dispose()
    }

    // 确保图表容器有尺寸
    chartRef.value.style.width = '100%'
    chartRef.value.style.height = '250px'

    // 创建新实例
    chartInstance.value = echarts.init(chartRef.value, isDarkMode.value ? 'dark' : null)

    // 检查数据是否有效
    const hasValidData = chartData.value && chartData.value.length > 0

    // 设置图表选项
    const option = {
      backgroundColor: 'transparent',
      title: {
        text: hasValidData
          ? `${chartAxisNames.value.yAxis}-${chartAxisNames.value.xAxis}曲线`
          : '等待数据输入...',
        textStyle: {
          fontSize: 13,
          fontWeight: 'normal',
          color: isDarkMode.value ? '#e2e8f0' : '#334155',
        },
        left: 'center',
        top: 5,
      },
      tooltip: {
        trigger: 'axis',
        backgroundColor: isDarkMode.value ? 'rgba(30, 41, 59, 0.9)' : 'rgba(255, 255, 255, 0.9)',
        borderColor: isDarkMode.value ? '#475569' : '#e2e8f0',
        textStyle: {
          color: isDarkMode.value ? '#e2e8f0' : '#334155',
        },
        formatter: function (params) {
          if (!params || params.length === 0 || !params[0].value) {
            return '暂无数据'
          }
          const data = params[0]
          return `${chartAxisNames.value.xAxis}: ${data.value[0]}<br>${
            chartAxisNames.value.yAxis
          }: ${data.value[1].toFixed(2)}`
        },
      },
      grid: {
        left: '8%',
        right: '5%',
        bottom: '15%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        name: chartAxisNames.value.xAxis,
        nameLocation: 'middle',
        nameGap: 25,
        axisLine: {
          lineStyle: {
            color: isDarkMode.value ? '#475569' : '#cbd5e1',
          },
        },
        axisLabel: {
          color: isDarkMode.value ? '#94a3b8' : '#64748b',
        },
        splitLine: {
          lineStyle: {
            color: isDarkMode.value ? 'rgba(71, 85, 105, 0.2)' : 'rgba(203, 213, 225, 0.5)',
          },
        },
      },
      yAxis: {
        type: 'value',
        name: chartAxisNames.value.yAxis,
        nameLocation: 'middle',
        nameGap: 35,
        axisLine: {
          lineStyle: {
            color: isDarkMode.value ? '#475569' : '#cbd5e1',
          },
        },
        axisLabel: {
          color: isDarkMode.value ? '#94a3b8' : '#64748b',
        },
        splitLine: {
          lineStyle: {
            color: isDarkMode.value ? 'rgba(71, 85, 105, 0.2)' : 'rgba(203, 213, 225, 0.5)',
          },
        },
      },
      dataZoom: hasValidData
        ? [
            {
              type: 'inside',
              xAxisIndex: 0,
              start: 0,
              end: 100,
            },
            {
              type: 'slider',
              xAxisIndex: 0,
              height: 15,
              bottom: 5,
              borderColor: 'transparent',
              backgroundColor: isDarkMode.value
                ? 'rgba(71, 85, 105, 0.2)'
                : 'rgba(203, 213, 225, 0.3)',
              fillerColor: isDarkMode.value ? 'rgba(59, 130, 246, 0.2)' : 'rgba(59, 130, 246, 0.1)',
              handleStyle: {
                color: '#3b82f6',
              },
              textStyle: {
                color: isDarkMode.value ? '#94a3b8' : '#64748b',
              },
            },
          ]
        : [],
      series: [
        {
          name: chartAxisNames.value.yAxis,
          type: 'line',
          data: hasValidData ? chartData.value : [],
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          sampling: 'average',
          lineStyle: {
            width: 3,
            color: '#3b82f6',
          },
          itemStyle: {
            color: '#3b82f6',
            borderColor: isDarkMode.value ? '#1e293b' : '#ffffff',
            borderWidth: 2,
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: isDarkMode.value ? 'rgba(59, 130, 246, 0.3)' : 'rgba(59, 130, 246, 0.3)',
              },
              {
                offset: 1,
                color: isDarkMode.value ? 'rgba(59, 130, 246, 0.05)' : 'rgba(59, 130, 246, 0.05)',
              },
            ]),
          },
        },
      ],
    }

    // 应用选项
    chartInstance.value.setOption(option)

    // 强制重绘一次
    setTimeout(() => {
      if (chartInstance.value) {
        chartInstance.value.resize()
      }
    }, 200)
  } catch (error) {
    console.error('图表初始化失败:', error.message, error.stack)
  }
}

// 计算节点背景色
const nodeBackgroundColor = computed(() => {
  // 如果节点自定义了背景色，优先使用
  if (props.data.backgroundColor) {
    return props.data.backgroundColor
  }

  // 如果没有背景色，则根据节点类型和主题计算
  const nodeType = props.data.nodeType || props.data.type
  const nodeCategory = props.data.category || ''

  return getNodeBackgroundColor(props.id, nodeType, nodeCategory)
})

// 监听连接事件
const handleEdgeConnect = (event) => {
  if (event.detail && event.detail.edge) {
    const { source, target } = event.detail.edge

    // 如果当前节点是目标节点
    if (target === props.id) {
      // 查找源节点数据并更新图表
      emitter.emit('request-node-data', {
        sourceId: source,
        targetId: props.id,
        callback: (sourceNodeData) => {
          updateChartData(sourceNodeData)
        },
      })
    }
  }
}

// 监听节点数据更新事件
const handleNodeDataUpdated = (event) => {
  if (event.detail) {
    const { nodeId, data } = event.detail

    // 如果是当前节点数据更新，直接使用
    if (nodeId === props.id) {
      updateChartData(data)
      return
    }

    // 查找连接到当前节点的边
    emitter.emit('get-connected-edges', {
      nodeId: props.id,
      callback: (edges) => {
        // 检查是否有来自更新节点的连接
        const connectedEdge = edges.find(
          (edge) => edge.source === nodeId && edge.target === props.id,
        )

        if (connectedEdge) {
          // 更新图表数据
          updateChartData(data)
        }
      },
    })
  }
}

// 窗口大小变化时重绘图表
const handleResize = () => {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 处理 gRPC 消息
const handleGrpcMessage = (event) => {
  try {
    const { type, data } = event.detail

    // 检查是否是 function-call 类型的消息，并且函数名是 loadData
    if (
      (type === 'function-call' && data?.functionName === 'loadData') ||
      data?.key_value_pairs?.function_name === 'loadData'
    ) {
      // 解析 call_params，处理两种可能的数据结构
      let callParams = null

      if (data.key_value_pairs?.call_params) {
        // 处理字符串形式的 call_params
        const rawParams = data.key_value_pairs.call_params
        callParams = typeof rawParams === 'string' ? JSON.parse(rawParams) : rawParams
      } else if (data.data?.key_value_pairs?.call_params) {
        // 处理嵌套在 data 中的 call_params
        const rawParams = data.data.key_value_pairs.call_params
        callParams = typeof rawParams === 'string' ? JSON.parse(rawParams) : rawParams
      }

      if (!callParams) {
        return
      }

      // 检查是否有节点数据
      if (callParams.nodes && Array.isArray(callParams.nodes)) {
        // 查找电池数据图表节点
        const chartNode = callParams.nodes.find(
          (node) => node.data.type === 'BatteryDataChart' || node.data.label === '电池数据图表',
        )

        if (chartNode && chartNode.data) {
          // 创建一个深拷贝，避免对象引用问题
          const safeData = JSON.parse(JSON.stringify(chartNode.data))

          // 直接使用节点数据更新图表
          updateChartData(safeData)
        }
      }
    }
  } catch (error) {
    console.error('处理 gRPC 消息失败:', error.message, error.stack)
  }
}

// 处理 gRPC 函数调用
const handleGrpcFunctionCall = (event) => {
  try {
    const { functionName, data } = event.detail

    if (functionName === 'loadData' && data) {
      try {
        // 解析 call_params
        let callParams = null

        if (data.key_value_pairs?.call_params) {
          // 处理字符串形式的 call_params
          const rawParams = data.key_value_pairs.call_params
          callParams = typeof rawParams === 'string' ? JSON.parse(rawParams) : rawParams
        }

        if (!callParams) {
          return
        }

        // 检查是否有节点数据
        if (callParams.nodes && Array.isArray(callParams.nodes)) {
          // 查找电池数据图表节点
          const chartNode = callParams.nodes.find(
            (node) => node.data.type === 'BatteryDataChart' || node.data.label === '电池数据图表',
          )

          if (chartNode && chartNode.data) {
            // 创建一个深拷贝，避免对象引用问题
            const safeData = JSON.parse(JSON.stringify(chartNode.data))

            // 直接使用节点数据更新图表
            updateChartData(safeData)
          }
        }
      } catch (error) {
        console.error('处理 grpc-function-call loadData 消息失败:', error.message, error.stack)
      }
    }
  } catch (error) {
    console.error('处理 grpc 事件失败:', error.message, error.stack)
  }
}

// 监听节点自身params变化
watch(
  () => props.data.params,
  (newParams) => {
    if (newParams && newParams.data) {
      loadDataFromNodeParams()
    }
  },
  { deep: true, immediate: true },
)

onMounted(() => {
  // 监听边连接事件
  emitter.on('edge-connected', handleEdgeConnect)

  // 监听节点数据更新事件
  emitter.on('node-data-updated', handleNodeDataUpdated)

  // 监听 gRPC 消息
  window.addEventListener('grpc-message', handleGrpcMessage)
  window.addEventListener('grpc-function-call', handleGrpcFunctionCall)

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize)

  // 初始化图表，优先使用节点自身的params数据
  nextTick(() => {
    if (props.data.params && props.data.params.data) {
      loadDataFromNodeParams()
    } else {
      // 请求已连接的节点数据
      emitter.emit('get-connected-edges', {
        nodeId: props.id,
        callback: (edges) => {
          if (edges && edges.length > 0) {
            // 获取源节点数据
            const sourceId = edges[0].source
            emitter.emit('request-node-data', {
              sourceId,
              targetId: props.id,
              callback: (sourceNodeData) => {
                if (sourceNodeData) {
                  updateChartData(sourceNodeData)
                }
              },
            })
          }
        },
      })
    }
  })
})

onBeforeUnmount(() => {
  // 清理事件监听
  emitter.off('edge-connected', handleEdgeConnect)
  emitter.off('node-data-updated', handleNodeDataUpdated)

  // 移除 gRPC 事件监听
  window.removeEventListener('grpc-message', handleGrpcMessage)
  window.removeEventListener('grpc-function-call', handleGrpcFunctionCall)

  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize)

  // 销毁图表实例
  if (chartInstance.value) {
    chartInstance.value.dispose()
    chartInstance.value = null
  }
})
</script>

<style lang="scss" scoped>
.battery-chart-node {
  @apply bg-card text-card-foreground border border-border rounded-lg shadow-sm p-3 relative;
  width: 380px;
  transition: all 0.2s ease;

  &.selected {
    @apply border-primary ring-1 ring-primary/30;
    transform: translateY(-2px);
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.05),
      0 4px 6px -2px rgba(0, 0, 0, 0.025);
  }

  .node-header {
    @apply flex items-center justify-between w-full mb-2;

    .node-icon {
      @apply flex items-center justify-center p-1.5 rounded-md bg-primary/10 text-primary mr-2;
      width: 32px;
      height: 32px;
      flex-shrink: 0;

      .icon {
        width: 20px;
        height: 20px;
      }
    }

    .node-title {
      @apply flex-1 text-base font-medium;
    }

    .delete-button {
      @apply text-muted-foreground hover:text-destructive;
    }
  }

  .chart-card {
    @apply mt-2 border-t pt-2 border-border/50;

    .no-data-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 250px;
      @apply flex items-center justify-center text-center;
      background-color: var(--background);
      z-index: 10;
    }

    .echarts-container {
      width: 100%;
      height: 250px;
    }
  }

  :deep(.vue-flow__handle) {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: hsl(var(--background));
    border: 2px solid hsl(var(--primary));
    transition: all 0.2s ease;
    position: absolute;
    z-index: 1;

    // 输入节点样式（target）
    &.handle-target {
      border: 2px solid hsl(var(--primary));
      background: hsl(var(--background));

      &::after {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border-radius: 50%;
        border: 2px solid transparent;
        transition: all 0.2s ease;
      }

      &:hover::after {
        border-color: hsl(var(--primary));
        transform: scale(1.2);
      }
    }
  }

  :deep(.vue-flow__handle-left) {
    left: -6px;
  }
}

/* 暗黑模式适配 */
.dark .battery-chart-node {
  @apply bg-card text-card-foreground border-border;

  &.selected {
    @apply border-primary ring-1 ring-primary/30;
  }
}
</style>
