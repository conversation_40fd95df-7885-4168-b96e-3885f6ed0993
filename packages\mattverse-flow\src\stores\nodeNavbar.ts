import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export interface NodeTab {
  id: string
  label: string
  nodeId: string
  nodeData: any
}

export const useNodeNavbarStore = defineStore('nodeNavbar', () => {
  const tabs = ref<NodeTab[]>([])
  const activeTabId = ref<string | null>(null)
  const showNodeSettings = ref(false)

  // 添加标签
  function addTab(nodeData: any) {
    const nodeId = nodeData.id
    const existingTab = tabs.value.find(tab => tab.nodeId === nodeId)

    if (existingTab) {
      // 如果标签已存在，激活它
      activeTabId.value = existingTab.id
    } else {
      // 创建新标签
      const newTab: NodeTab = {
        id: `tab-${Date.now()}`,
        label: nodeData.data?.label || '未命名节点',
        nodeId: nodeId,
        nodeData: nodeData,
      }

      tabs.value.push(newTab)
      activeTabId.value = newTab.id
    }

    // 显示设置面板
    showNodeSettings.value = true
  }

  // 移除标签
  function removeTab(tabId: string) {
    const index = tabs.value.findIndex(tab => tab.id === tabId)

    if (index > -1) {
      // 如果删除的是当前活动的标签
      if (tabId === activeTabId.value) {
        // 如果有其他标签，选择下一个或前一个
        if (tabs.value.length > 1) {
          const nextTab = tabs.value[index + 1] || tabs.value[index - 1]
          activeTabId.value = nextTab.id
        } else {
          // 如果是最后一个标签，隐藏设置面板
          activeTabId.value = null
          showNodeSettings.value = false
        }
      }

      // 移除标签
      tabs.value.splice(index, 1)

      // 如果没有标签了，隐藏设置面板
      if (tabs.value.length === 0) {
        showNodeSettings.value = false
      }
    }
  }
  // 关闭所有标签
  function closeAllTabs() {
    tabs.value = []
    activeTabId.value = null
    showNodeSettings.value = false
  }

  // 关闭右侧标签
  function closeRightTabs(tabId: string) {
    const index = tabs.value.findIndex(tab => tab.id === tabId)
    if (index > -1) {
      // 保留当前及左侧标签，移除右侧标签
      const rightTabs = tabs.value.slice(index + 1)
      tabs.value = tabs.value.slice(0, index + 1)

      // 如果当前活动标签在被删除的标签中，则激活当前标签
      if (rightTabs.some(tab => tab.id === activeTabId.value)) {
        activeTabId.value = tabId
      }
    }
  }

  // 关闭左侧标签
  function closeLeftTabs(tabId: string) {
    const index = tabs.value.findIndex(tab => tab.id === tabId)
    if (index > -1) {
      // 保留当前及右侧标签，移除左侧标签
      const leftTabs = tabs.value.slice(0, index)
      tabs.value = tabs.value.slice(index)

      // 如果当前活动标签在被删除的标签中，则激活当前标签
      if (leftTabs.some(tab => tab.id === activeTabId.value)) {
        activeTabId.value = tabId
      }
    }
  }
  // 设置活动标签
  function setActiveTab(tabId: string) {
    activeTabId.value = tabId
  }

  // 更新节点数据
  function updateNodeData(nodeId: string, newData: any) {
    const tab = tabs.value.find(tab => tab.nodeId === nodeId)
    if (tab) {
      tab.nodeData = newData
      tab.label = newData.data?.label || '未命名节点'
    }
  }

  // 获取当前活动的标签数据
  const activeTabData = computed(() => {
    if (!activeTabId.value) return null
    return tabs.value.find(tab => tab.id === activeTabId.value)
  })

  // 重置状态
  function reset() {
    tabs.value = []
    activeTabId.value = null
    showNodeSettings.value = false
  }

  return {
    tabs,
    activeTabId,
    showNodeSettings,
    addTab,
    removeTab,
    closeAllTabs,
    closeRightTabs,
    closeLeftTabs,
    setActiveTab,
    updateNodeData,
    activeTabData,
    reset,
  }
})
