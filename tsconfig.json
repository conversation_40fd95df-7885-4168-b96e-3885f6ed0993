{"extends": "./packages/configs/src/typescript/base.json", "compilerOptions": {"baseUrl": ".", "paths": {"@mattverse/shared": ["packages/shared/src"], "@mattverse/mattverse-ui": ["packages/mattverse-ui/src"], "@mattverse/mattverse-flow": ["packages/mattverse-flow/src"], "@mattverse/electron-core": ["packages/electron-core/src"], "@mattverse/configs": ["packages/configs/src"]}}, "include": ["apps/*/src/**/*", "packages/*/src/**/*", "tools/*/src/**/*"], "exclude": ["node_modules", "**/dist", "**/node_modules"], "references": [{"path": "./packages/shared"}, {"path": "./packages/configs"}, {"path": "./packages/mattverse-ui"}, {"path": "./packages/mattverse-flow"}, {"path": "./packages/electron-core"}, {"path": "./apps/mattverse"}, {"path": "./apps/highpower"}]}