<template>
  <Card
    class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
  >
    <CardHeader>
      <CardTitle class="flex items-center space-x-2">
        <MattIcon name="Cog" class="h-5 w-5" />
        <span>{{ $t('settings.middleware.advanced_settings') }}</span>
      </CardTitle>
      <CardDescription>
        {{ $t('settings.middleware.advanced_settings_desc') }}
      </CardDescription>
    </CardHeader>
    <CardContent class="space-y-6">
      <!-- 连接超时 -->
      <div class="space-y-3">
        <Label class="text-sm"
          >{{ $t('settings.middleware.connection_timeout') }} ({{ $t('common.seconds') }})</Label
        >
        <div class="flex items-center space-x-4">
          <Slider
            :model-value="[config.timeout]"
            :max="60"
            :min="5"
            :step="5"
            class="flex-1"
            @update:model-value="$emit('update:config', { ...config, timeout: $event[0] })"
          />
          <span class="text-sm text-muted-foreground w-12">{{ config.timeout }}s</span>
        </div>
      </div>

      <Separator />

      <!-- 自动重连 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{ $t('settings.middleware.auto_reconnect') }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.middleware.auto_reconnect_desc') }}
          </p>
        </div>
        <Switch
          :model-value="config.autoReconnect"
          @update:model-value="$emit('update:config', { ...config, autoReconnect: $event })"
        />
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { useI18n } from '@mattverse/i18n'
import type { MiddlewareConfig } from '@/store'

interface Props {
  config: MiddlewareConfig
}

defineProps<Props>()
defineEmits<{
  'update:config': [value: MiddlewareConfig]
}>()

// 国际化
const { t } = useI18n()
</script>
