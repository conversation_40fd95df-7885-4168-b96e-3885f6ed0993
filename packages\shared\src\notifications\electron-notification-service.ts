/**
 * Electron 环境下的通知服务实现
 * 整合 vue-sonner toast 和 Electron 桌面通知
 */
import {
  BaseNotificationService,
  type BaseNotificationOptions,
  type NotificationType,
  type DesktopNotificationOptions,
  type NotificationSettings,
} from './notification-service'

// 扩展选项，添加 type 属性
interface ElectronNotificationOptions extends BaseNotificationOptions {
  type?: NotificationType
}

// Toast 函数类型定义（来自 vue-sonner）
interface ToastFunction {
  (message: string, options?: any): string | number
  success: (message: string, options?: any) => string | number
  error: (message: string, options?: any) => string | number
  warning: (message: string, options?: any) => string | number
  info: (message: string, options?: any) => string | number
  loading: (message: string, options?: any) => string | number
  dismiss: (id?: string | number) => void
}

// 使用现有的 ElectronAPI 类型定义

export class ElectronNotificationService extends BaseNotificationService {
  private toastFunction?: ToastFunction
  private logger?: any

  constructor(
    toastFunction?: ToastFunction,
    logger?: any,
    initialSettings?: Partial<NotificationSettings>
  ) {
    super(initialSettings)
    this.toastFunction = toastFunction
    this.logger = logger
  }

  // 设置 toast 函数（在 Vue 应用中初始化时调用）
  setToastFunction(toastFunction: ToastFunction): void {
    this.toastFunction = toastFunction
  }

  // 设置日志器
  setLogger(logger: any): void {
    this.logger = logger
  }

  // 主要的显示通知方法
  async show(message: string, options: ElectronNotificationOptions = {}): Promise<string | number> {
    const {
      type = 'default',
      title,
      description,
      showToast = true,
      showDesktop = true,
      playSound = true,
      ...restOptions
    } = options

    let toastId: string | number = ''

    try {
      // 显示应用内通知 (Toast)
      if (showToast && this.shouldShowToast(options)) {
        toastId = await this.showToast(message, type, {
          description: description || title,
          ...restOptions,
        })
      }

      // 显示桌面通知
      if (showDesktop && this.shouldShowDesktop(options)) {
        await this.showDesktopNotification(
          title || message,
          description || '',
          options.icon,
          options.action ? [{ type: 'button', text: options.action.label }] : undefined
        )
      }

      // 播放声音通知
      if (playSound && this.shouldPlaySound(options)) {
        this.playNotificationSound(type)
      }

      this.logger?.info('Notification shown:', {
        message,
        title,
        description,
        type,
        showToast: this.shouldShowToast(options),
        showDesktop: this.shouldShowDesktop(options),
        playSound: this.shouldPlaySound(options),
      })

      return toastId
    } catch (error) {
      this.logger?.error('Failed to show notification:', error)
      throw error
    }
  }

  // 显示应用内通知 (Toast)
  private async showToast(
    message: string,
    type: NotificationType,
    options: any = {}
  ): Promise<string | number> {
    if (!this.toastFunction) {
      throw new Error('Toast function not initialized. Call setToastFunction() first.')
    }

    // 根据类型调用相应的 toast 方法
    switch (type) {
      case 'success':
        return this.toastFunction.success(message, options)
      case 'error':
        return this.toastFunction.error(message, options)
      case 'warning':
        return this.toastFunction.warning(message, options)
      case 'info':
        return this.toastFunction.info(message, options)
      case 'loading':
        return this.toastFunction.loading(message, options)
      default:
        return this.toastFunction(message, options)
    }
  }

  // 显示桌面通知
  private async showDesktopNotification(
    title: string,
    body: string = '',
    icon?: string,
    actions?: Array<{ type: 'button'; text: string }>
  ): Promise<void> {
    if (!window.electronAPI) {
      throw new Error('Electron API not available')
    }

    try {
      // 检查是否支持桌面通知
      const isSupported = await window.electronAPI.invoke('notification:is-supported')
      if (!isSupported) {
        this.logger?.warn('Desktop notifications are not supported')
        return
      }

      // 请求通知权限
      const permission = await window.electronAPI.invoke('notification:request-permission')
      if (!permission.granted) {
        this.logger?.warn('Desktop notification permission not granted')
        return
      }

      // 显示桌面通知
      const options: DesktopNotificationOptions = {
        title,
        body,
        icon,
        actions,
        silent: false,
      }

      const result = await window.electronAPI.invoke('notification:show', options)
      if (!result.success) {
        throw new Error(result.error || 'Failed to show desktop notification')
      }
    } catch (error) {
      this.logger?.error('Desktop notification error:', error)
      throw error
    }
  }

  // 关闭通知
  dismiss(id?: string | number): void {
    if (this.toastFunction) {
      this.toastFunction.dismiss(id)
    }
  }

  // 测试通知功能
  async test(): Promise<void> {
    await this.info('通知测试', {
      description: '这是一个测试通知，用于验证通知功能是否正常工作',
      duration: 5000,
    })
  }
}

// 创建默认实例（延迟初始化）
let defaultService: ElectronNotificationService | null = null

// 获取默认服务实例
export function getNotificationService(): ElectronNotificationService {
  if (!defaultService) {
    defaultService = new ElectronNotificationService()
  }
  return defaultService
}

// 初始化通知服务
export function initNotificationService(
  toastFunction: ToastFunction,
  logger?: any,
  settings?: Partial<NotificationSettings>
): ElectronNotificationService {
  defaultService = new ElectronNotificationService(toastFunction, logger, settings)
  return defaultService
}

// 便捷导出
export const notify = {
  show: (message: string, options?: ElectronNotificationOptions) =>
    getNotificationService().show(message, options),
  success: (message: string, options?: BaseNotificationOptions) =>
    getNotificationService().success(message, options),
  error: (message: string, options?: BaseNotificationOptions) =>
    getNotificationService().error(message, options),
  warning: (message: string, options?: BaseNotificationOptions) =>
    getNotificationService().warning(message, options),
  info: (message: string, options?: BaseNotificationOptions) =>
    getNotificationService().info(message, options),
  loading: (message: string, options?: BaseNotificationOptions) =>
    getNotificationService().loading(message, options),
  dismiss: (id?: string | number) => getNotificationService().dismiss(id),
  test: () => getNotificationService().test(),
}
